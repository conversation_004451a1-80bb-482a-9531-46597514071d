#!/usr/bin/env python3
"""Test script to validate the refactored PySwarms optimizers.

This script tests that the refactored PySwarms optimizers work correctly
and maintain compatibility with the original implementation.
"""

import numpy as np
import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'optimagic', 'src'))

try:
    from optimagic.optimizers.pyswarms_optimizers import (
        PySwarmsGlobalBestPSO,
        PySwarmsLocalBestPSO,
        PySwarmsGeneralPSO,
        BasePSOOptions,
        LocalBestPSOOptions,
        GeneralPSOOptions,
        _build_pso_options_dict,
        _build_velocity_clamp,
        _create_topology_instance,
        _convert_bounds_to_pyswarms,
        _create_objective_wrapper,
        _process_pyswarms_result,
    )
    from optimagic.optimization.internal_optimization_problem import InternalBounds
    from optimagic.config import IS_PYSWARMS_INSTALLED
    print("✓ Successfully imported refactored PySwarms optimizers")
except ImportError as e:
    print(f"✗ Failed to import refactored PySwarms optimizers: {e}")
    sys.exit(1)


def test_dataclasses():
    """Test that the dataclasses work correctly."""
    print("\n=== Testing Dataclasses ===")
    
    # Test BasePSOOptions
    base_options = BasePSOOptions(
        cognitive_parameter=0.5,
        social_parameter=0.3,
        inertia_weight=0.9
    )
    print(f"✓ BasePSOOptions created: {base_options}")
    
    # Test LocalBestPSOOptions
    local_options = LocalBestPSOOptions(
        cognitive_parameter=0.5,
        social_parameter=0.3,
        inertia_weight=0.9,
        k_neighbors=3,
        p_norm=2
    )
    print(f"✓ LocalBestPSOOptions created: {local_options}")
    
    # Test GeneralPSOOptions
    general_options = GeneralPSOOptions(
        cognitive_parameter=0.5,
        social_parameter=0.3,
        inertia_weight=0.9,
        k_neighbors=3,
        p_norm=2,
        vonneumann_range=1
    )
    print(f"✓ GeneralPSOOptions created: {general_options}")


def test_helper_functions():
    """Test that the helper functions work correctly."""
    print("\n=== Testing Helper Functions ===")
    
    # Test _build_pso_options_dict
    base_options = BasePSOOptions(
        cognitive_parameter=0.5,
        social_parameter=0.3,
        inertia_weight=0.9
    )
    options_dict = _build_pso_options_dict(base_options)
    expected = {'c1': 0.5, 'c2': 0.3, 'w': 0.9}
    assert options_dict == expected, f"Expected {expected}, got {options_dict}"
    print(f"✓ _build_pso_options_dict works for BasePSOOptions: {options_dict}")
    
    # Test with LocalBestPSOOptions
    local_options = LocalBestPSOOptions(
        cognitive_parameter=0.5,
        social_parameter=0.3,
        inertia_weight=0.9,
        k_neighbors=3,
        p_norm=2
    )
    options_dict = _build_pso_options_dict(local_options)
    expected = {'c1': 0.5, 'c2': 0.3, 'w': 0.9, 'k': 3, 'p': 2}
    assert options_dict == expected, f"Expected {expected}, got {options_dict}"
    print(f"✓ _build_pso_options_dict works for LocalBestPSOOptions: {options_dict}")
    
    # Test _build_velocity_clamp
    velocity_clamp = _build_velocity_clamp(-1.0, 1.0)
    assert velocity_clamp == (-1.0, 1.0), f"Expected (-1.0, 1.0), got {velocity_clamp}"
    print(f"✓ _build_velocity_clamp works: {velocity_clamp}")
    
    velocity_clamp_none = _build_velocity_clamp(None, 1.0)
    assert velocity_clamp_none is None, f"Expected None, got {velocity_clamp_none}"
    print(f"✓ _build_velocity_clamp returns None when incomplete: {velocity_clamp_none}")
    
    # Test _convert_bounds_to_pyswarms
    bounds = InternalBounds(
        lower=np.array([0.0, -1.0]),
        upper=np.array([1.0, 1.0])
    )
    pyswarms_bounds = _convert_bounds_to_pyswarms(bounds)
    assert pyswarms_bounds is not None, "Expected bounds conversion to succeed"
    assert np.array_equal(pyswarms_bounds[0], np.array([0.0, -1.0])), "Lower bounds mismatch"
    assert np.array_equal(pyswarms_bounds[1], np.array([1.0, 1.0])), "Upper bounds mismatch"
    print(f"✓ _convert_bounds_to_pyswarms works: {pyswarms_bounds}")


def test_optimizer_instantiation():
    """Test that the optimizers can be instantiated correctly."""
    print("\n=== Testing Optimizer Instantiation ===")
    
    # Test GlobalBestPSO
    global_pso = PySwarmsGlobalBestPSO()
    print(f"✓ PySwarmsGlobalBestPSO instantiated: {global_pso.name}")
    
    # Test LocalBestPSO
    local_pso = PySwarmsLocalBestPSO()
    print(f"✓ PySwarmsLocalBestPSO instantiated: {local_pso.name}")
    
    # Test GeneralPSO
    general_pso = PySwarmsGeneralPSO()
    print(f"✓ PySwarmsGeneralPSO instantiated: {general_pso.name}")


def test_topology_creation():
    """Test topology creation if PySwarms is available."""
    print("\n=== Testing Topology Creation ===")
    
    if not IS_PYSWARMS_INSTALLED:
        print("⚠ PySwarms not installed, skipping topology tests")
        return
    
    try:
        # Test all topology types
        topologies = ["star", "ring", "vonneumann", "random"]
        for topology_type in topologies:
            topology = _create_topology_instance(topology_type)
            print(f"✓ Created {topology_type} topology: {type(topology).__name__}")
    except Exception as e:
        print(f"✗ Topology creation failed: {e}")


def main():
    """Run all tests."""
    print("Testing Refactored PySwarms Optimizers")
    print("=" * 50)
    
    try:
        test_dataclasses()
        test_helper_functions()
        test_optimizer_instantiation()
        test_topology_creation()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed! The refactored code works correctly.")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
