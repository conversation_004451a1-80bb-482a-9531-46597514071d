"""Implement PySwarms particle swarm optimization algorithms.

This module provides optimagic-compatible wrappers for PySwarms particle swarm
optimization algorithms including global best, local best, and general PSO variants
with support for different topologies.

"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Literal

import numpy as np
from numpy.typing import NDArray

from optimagic import mark
from optimagic.config import IS_PYSWARMS_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimization.algo_options import STOPPING_MAXFUN_GLOBAL, STOPPING_MAXITER
from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult
from optimagic.optimization.internal_optimization_problem import (
    InternalBounds,
    InternalOptimizationProblem,
)
from optimagic.typing import AggregationLevel, NonNegativeFloat, PositiveFloat, PositiveInt

PYSWARMS_NOT_INSTALLED_ERROR = (
    "This optimizer requires the 'pyswarms' package to be installed. "
    "You can install it with `pip install pyswarms`. "
    "Visit https://pyswarms.readthedocs.io/en/latest/installation.html "
    "for more detailed installation instructions."
)


# ======================================================================================
# Dataclasses for PySwarms Options
# ======================================================================================


@dataclass(frozen=True)
class BasePSOOptions:
    """Base dataclass for common PSO parameters.

    This dataclass contains the core parameters shared across all PSO variants
    including cognitive and social parameters, inertia weight, and basic
    convergence criteria.
    """

    cognitive_parameter: PositiveFloat
    """Cognitive parameter c1 controlling attraction to personal best.

    This parameter controls how much a particle is attracted to its own best
    position found so far. Higher values increase exploitation of personal
    experience. Typical values range from 0.5 to 2.5.
    """

    social_parameter: PositiveFloat
    """Social parameter c2 controlling attraction to neighborhood/global best.

    This parameter controls how much a particle is attracted to the best
    position found by its neighborhood or the entire swarm. Higher values
    increase exploitation of collective knowledge. Typical values range from 0.5 to 2.5.
    """

    inertia_weight: PositiveFloat
    """Inertia weight w controlling momentum of particle movement.

    This parameter controls the influence of the particle's previous velocity.
    Higher values promote exploration, lower values promote exploitation.
    Values typically range from 0.1 to 0.9, with 0.9 being a common choice.
    """


@dataclass(frozen=True)
class LocalBestPSOOptions(BasePSOOptions):
    """Options specific to Local Best PSO algorithm.

    Extends the base PSO options with parameters specific to local best PSO
    including neighborhood size and distance metric configuration.
    """

    k_neighbors: PositiveInt
    """Number of neighbors k defining the local neighborhood.

    Each particle's neighborhood consists of its k nearest neighbors
    based on the Minkowski p-norm distance. Larger values increase information
    sharing but may reduce diversity.
    """

    p_norm: Literal[1, 2]
    """Minkowski p-norm for distance calculation in neighborhood selection.

    - 1: Manhattan distance (L1 norm)
    - 2: Euclidean distance (L2 norm)

    The L2 norm is more commonly used and typically provides better performance.
    """


@dataclass(frozen=True)
class GeneralPSOOptions(BasePSOOptions):
    """Options specific to General PSO algorithm with topology support.

    Extends the base PSO options with parameters for configuring different
    topology types and their specific parameters.
    """

    k_neighbors: PositiveInt | None = None
    """Number of neighbors for ring, vonneumann, and random topologies.

    Only used when topology requires neighborhood definition. Defines the
    neighborhood size for each particle.
    """

    p_norm: Literal[1, 2] | None = None
    """Minkowski p-norm for distance calculation in neighborhood selection.

    Used by topologies that require distance-based neighbor selection.
    """

    vonneumann_range: PositiveInt | None = None
    """Range parameter r for Von Neumann topology.

    Defines the range of connections in the 2D grid. Higher values create
    larger neighborhoods but may reduce the benefits of the grid structure.
    Only used when topology_type is 'vonneumann'.
    """


# ======================================================================================
# Internal Helper Methods
# ======================================================================================


def _build_pso_options_dict(options: BasePSOOptions) -> dict[str, float | int]:
    """Build PySwarms options dictionary from structured options dataclass.

    Args:
        options: Structured PSO options dataclass containing parameters.

    Returns:
        Dictionary in PySwarms format with parameter names as keys.
    """
    base_options = {
        'c1': options.cognitive_parameter,
        'c2': options.social_parameter,
        'w': options.inertia_weight,
    }

    # Add topology-specific options if present
    if isinstance(options, LocalBestPSOOptions):
        base_options.update({
            'k': options.k_neighbors,
            'p': options.p_norm,
        })
    elif isinstance(options, GeneralPSOOptions):
        if options.k_neighbors is not None:
            base_options['k'] = options.k_neighbors
        if options.p_norm is not None:
            base_options['p'] = options.p_norm
        if options.vonneumann_range is not None:
            base_options['r'] = options.vonneumann_range

    return base_options


def _build_velocity_clamp(
    velocity_clamp_min: float | None,
    velocity_clamp_max: float | None
) -> tuple[float, float] | None:
    """Build velocity clamp tuple for PySwarms.

    Args:
        velocity_clamp_min: Minimum velocity value for clamping.
        velocity_clamp_max: Maximum velocity value for clamping.

    Returns:
        Tuple of (min, max) velocity clamps if both are provided, None otherwise.
    """
    if velocity_clamp_min is not None and velocity_clamp_max is not None:
        return (velocity_clamp_min, velocity_clamp_max)
    return None


def _create_topology_instance(topology_type: str):
    """Create PySwarms topology instance based on topology type.

    Args:
        topology_type: String identifier for the topology type.

    Returns:
        PySwarms topology instance.

    Raises:
        ImportError: If PySwarms is not installed.
        ValueError: If topology_type is not recognized.
    """
    if not IS_PYSWARMS_INSTALLED:
        raise NotInstalledError(PYSWARMS_NOT_INSTALLED_ERROR)

    from pyswarms.backend.topology import Random, Ring, Star, VonNeumann

    topology_map = {
        "star": Star(),
        "ring": Ring(),
        "vonneumann": VonNeumann(),
        "random": Random(),
    }

    if topology_type not in topology_map:
        raise ValueError(f"Unknown topology type: {topology_type}")

    return topology_map[topology_type]


def _process_pyswarms_result(
    result: tuple[float, NDArray[np.float64]]
) -> InternalOptimizeResult:
    """Process PySwarms optimization result into optimagic format.

    Args:
        result: Tuple containing (best_cost, best_position) from PySwarms.

    Returns:
        InternalOptimizeResult with standardized format.
    """
    best_cost, best_position = result

    return InternalOptimizeResult(
        x=best_position,
        fun=best_cost,
        success=True,
        message="PySwarms optimization completed",
    )


def _create_objective_wrapper(problem: InternalOptimizationProblem):
    """Create objective function wrapper for PySwarms.

    PySwarms passes a 2D array of shape (n_particles, n_dimensions) to the
    objective function and expects a 1D array of shape (n_particles,) back.
    Each row represents one particle's position.

    Args:
        problem: Internal optimization problem containing the objective function.

    Returns:
        Wrapped objective function compatible with PySwarms format.
    """
    def objective_wrapper(x: NDArray[np.float64]) -> NDArray[np.float64]:
        """Objective wrapper for PySwarms format.

        Args:
            x: 2D array of shape (n_particles, n_dimensions) with particle positions.

        Returns:
            1D array of shape (n_particles,) with objective values.
        """
        return np.array([problem.fun(xi) for xi in x])

    return objective_wrapper


def _convert_bounds_to_pyswarms(
    bounds: InternalBounds | None
) -> tuple[NDArray[np.float64], NDArray[np.float64]] | None:
    """Convert optimagic InternalBounds to PySwarms format.

    PySwarms expects bounds as a tuple of (lower_bounds, upper_bounds) where both
    are numpy arrays of the same shape as the parameter vector. If bounds are None
    or contain infinite values, PySwarms will operate without bounds.

    Args:
        bounds: Internal bounds object containing lower and upper bounds arrays.

    Returns:
        Tuple of (lower_bounds, upper_bounds) arrays if valid finite bounds exist,
        None otherwise.
    """
    if bounds is None:
        return None

    if bounds.lower is None or bounds.upper is None:
        return None

    # Check if all bounds are finite (PySwarms doesn't support infinite bounds)
    if not (np.all(np.isfinite(bounds.lower)) and np.all(np.isfinite(bounds.upper))):
        return None

    return (bounds.lower, bounds.upper)


@mark.minimizer(
    name="pyswarms_global_best",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYSWARMS_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    needs_bounds=False,
    supports_parallelism=True,
    supports_bounds=True,
    supports_infinite_bounds=False,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=True,
)
@dataclass(frozen=True)
class PySwarmsGlobalBestPSO(Algorithm):
    r"""Minimize a scalar function using Global Best Particle Swarm Optimization.

    This algorithm implements the global best PSO variant where each particle is
    influenced by the best position found by the entire swarm. It uses a star
    topology where all particles are connected to the global best particle.

    The position update is defined as:

    .. math::

        x_i(t+1) = x_i(t) + v_i(t+1)

    where the velocity update follows:

    .. math::

        v_{ij}(t+1) = w \cdot v_{ij}(t) + c_1 r_{1j}(t)[y_{ij}(t) - x_{ij}(t)]
                      + c_2 r_{2j}(t)[\hat{y}_j(t) - x_{ij}(t)]

    Here, :math:`c_1` and :math:`c_2` are the cognitive and social parameters that
    control the particle's behavior between following its personal best :math:`y_{ij}`
    or the swarm's global best :math:`\hat{y}_j`. The inertia weight :math:`w` controls
    the momentum of the swarm's movement. :math:`r_{1j}` and :math:`r_{2j}` are random
    numbers drawn from a uniform distribution [0,1].

    This algorithm is well-suited for global optimization problems with multiple local
    optima. It performs well on continuous optimization problems but does not require
    gradient information. The global best topology promotes fast convergence but may
    lead to premature convergence on multimodal problems.

    The algorithm was originally proposed by Kennedy and Eberhart (1995) and this
    implementation uses the PySwarms library.

    References:
        Kennedy, J., & Eberhart, R. (1995). Particle swarm optimization. Proceedings
        of ICNN'95-international conference on neural networks (Vol. 4, pp. 1942-1948).

    """

    n_particles: PositiveInt = 50
    """Number of particles in the swarm.

    Larger swarms can explore the search space more thoroughly but require more
    function evaluations per iteration. Typical values range from 20 to 100.
    """

    cognitive_parameter: PositiveFloat = 0.5
    r"""Cognitive parameter :math:`c_1` controlling attraction to personal best.

    This parameter controls how much a particle is attracted to its own best
    position found so far. Higher values increase exploitation of personal
    experience. Typical values range from 0.5 to 2.5.
    """

    social_parameter: PositiveFloat = 0.3
    r"""Social parameter :math:`c_2` controlling attraction to global best.

    This parameter controls how much a particle is attracted to the global best
    position found by the swarm. Higher values increase exploitation of swarm
    knowledge. Typical values range from 0.5 to 2.5.
    """

    inertia_weight: PositiveFloat = 0.9
    r"""Inertia weight :math:`w` controlling momentum of particle movement.

    This parameter controls the influence of the particle's previous velocity.
    Higher values promote exploration, lower values promote exploitation.
    Values typically range from 0.1 to 0.9, with 0.9 being a common choice.
    """

    convergence_ftol_rel: NonNegativeFloat = -np.inf
    r"""Relative tolerance for convergence based on function value changes.

    The algorithm converges when the relative change in the best function value
    over the last ``convergence_ftol_iter`` iterations is below this threshold.
    Set to -np.inf to disable this convergence criterion.
    """

    convergence_ftol_iter: PositiveInt = 1
    """Number of iterations to check for function value convergence.

    The algorithm checks convergence over this many recent iterations.
    """

    stopping_maxiter: PositiveInt = STOPPING_MAXITER
    """Maximum number of iterations before stopping."""

    stopping_maxfun: PositiveInt = STOPPING_MAXFUN_GLOBAL
    """Maximum number of function evaluations before stopping.

    The default value is set lower than local optimizers since global optimizers
    typically require more function evaluations.
    """

    boundary_strategy: Literal["periodic", "reflective", "shrink", "random", "intermediate"] = "periodic"
    """Strategy for handling particles that move outside the bounds.

    - 'periodic': Particles wrap around to the opposite boundary
    - 'reflective': Particles bounce off boundaries
    - 'shrink': Particles are moved to the boundary
    - 'random': Particles are randomly repositioned within bounds
    - 'intermediate': Particles are placed at random positions between current and boundary
    """

    velocity_strategy: Literal["unmodified", "adjust", "invert", "zero"] = "unmodified"
    """Strategy for handling velocities of out-of-bounds particles.

    - 'unmodified': Keep original velocity
    - 'adjust': Scale velocity to stay within bounds
    - 'invert': Reverse velocity direction
    - 'zero': Set velocity to zero
    """

    velocity_clamp_min: float | None = None
    """Minimum velocity value for clamping. Set to None to disable."""

    velocity_clamp_max: float | None = None
    """Maximum velocity value for clamping. Set to None to disable."""

    n_processes: PositiveInt | None = None
    """Number of processes for parallel function evaluation.

    Set to None to disable parallelization. When enabled, function evaluations
    for different particles are computed in parallel.
    """

    center_init: PositiveFloat = 1.0 
    """Scaling factor for initial particle positions.

    This parameter affects the initial distribution of particles around the
    center of the search space.
    """
    
    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_PYSWARMS_INSTALLED:
            raise NotInstalledError(PYSWARMS_NOT_INSTALLED_ERROR)

        import pyswarms as ps

        # Build structured options using dataclass
        pso_options = BasePSOOptions(
            cognitive_parameter=self.cognitive_parameter,
            social_parameter=self.social_parameter,
            inertia_weight=self.inertia_weight,
        )
        options = _build_pso_options_dict(pso_options)

        # Use helper method for velocity clamping
        velocity_clamp = _build_velocity_clamp(
            self.velocity_clamp_min, self.velocity_clamp_max
        )

        bounds = _convert_bounds_to_pyswarms(problem.bounds)

        optimizer = ps.single.GlobalBestPSO(
            n_particles=self.n_particles,
            dimensions=len(x0),
            options=options,
            bounds=bounds,
            bh_strategy=self.boundary_strategy,
            velocity_clamp=velocity_clamp,
            vh_strategy=self.velocity_strategy,
            center=self.center_init,
            ftol=self.convergence_ftol_rel,
            ftol_iter=self.convergence_ftol_iter,
            init_pos=None,
        )

        objective_wrapper = _create_objective_wrapper(problem)

        result = optimizer.optimize(
            objective_func=objective_wrapper,
            iters=self.stopping_maxiter,
            n_processes=self.n_processes,
            verbose=False,
        )

        return _process_pyswarms_result(result)


@mark.minimizer(
    name="pyswarms_local_best",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYSWARMS_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    needs_bounds=False,
    supports_parallelism=True,
    supports_bounds=True,
    supports_infinite_bounds=False,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=True,
)
@dataclass(frozen=True)
class PySwarmsLocalBestPSO(Algorithm):
    r"""Minimize a scalar function using Local Best Particle Swarm Optimization.

    This algorithm implements the local best PSO variant where each particle is
    influenced by the best position found within its local neighborhood rather
    than the global best. This promotes diversity and can help avoid premature
    convergence on multimodal problems.

    The velocity update equation is:

    .. math::

        v_{ij}(t+1) = w \cdot v_{ij}(t) + c_1 r_{1j}(t)[y_{ij}(t) - x_{ij}(t)]
                      + c_2 r_{2j}(t)[\hat{y}_{lj}(t) - x_{ij}(t)]

    where :math:`\hat{y}_{lj}(t)` is the best position found within the local
    neighborhood of particle :math:`i`, defined by the :math:`k` nearest neighbors
    according to the Minkowski p-norm distance.

    The local best topology helps maintain population diversity and can be more
    effective than global best PSO on highly multimodal problems. However, it may
    converge more slowly on unimodal problems due to reduced information sharing.

    This implementation uses PySwarms' LocalBestPSO with a ring topology where
    each particle's neighborhood consists of its :math:`k` nearest neighbors.

    References:
        Kennedy, J. (1999). Small worlds and mega-minds: effects of neighborhood
        topology on particle swarm performance. Proceedings of the 1999 congress
        on evolutionary computation (Vol. 3, pp. 1931-1938).

    """

    n_particles: PositiveInt = 50
    """Number of particles in the swarm."""

    cognitive_parameter: PositiveFloat = 0.5
    r"""Cognitive parameter :math:`c_1` controlling attraction to personal best."""

    social_parameter: PositiveFloat = 0.3
    r"""Social parameter :math:`c_2` controlling attraction to local best."""

    inertia_weight: PositiveFloat = 0.9
    r"""Inertia weight :math:`w` controlling momentum of particle movement."""

    k_neighbors: PositiveInt = 3
    r"""Number of neighbors :math:`k` defining the local neighborhood.

    Each particle's neighborhood consists of its :math:`k` nearest neighbors
    based on the Minkowski p-norm distance. Larger values increase information
    sharing but may reduce diversity.
    """

    p_norm: Literal[1, 2] = 2
    r"""Minkowski p-norm for distance calculation in neighborhood selection.

    - 1: Manhattan distance (L1 norm)
    - 2: Euclidean distance (L2 norm)

    The L2 norm is more commonly used and typically provides better performance.
    """
    convergence_ftol_rel: NonNegativeFloat = -np.inf
    r"""Relative tolerance for convergence based on function value changes.

    The algorithm converges when the relative change in the best function value
    over the last ``convergence_ftol_iter`` iterations is below this threshold.
    Set to -np.inf to disable this convergence criterion.
    """

    convergence_ftol_iter: PositiveInt = 1
    """Number of iterations to check for function value convergence.

    The algorithm checks convergence over this many recent iterations.
    """

    stopping_maxiter: PositiveInt = STOPPING_MAXITER
    """Maximum number of iterations before stopping."""

    stopping_maxfun: PositiveInt = STOPPING_MAXFUN_GLOBAL
    """Maximum number of function evaluations before stopping.

    The default value is set lower than local optimizers since global optimizers
    typically require more function evaluations.
    """

    boundary_strategy: Literal["periodic", "reflective", "shrink", "random", "intermediate"] = "periodic"
    """Strategy for handling particles that move outside the bounds.

    - 'periodic': Particles wrap around to the opposite boundary
    - 'reflective': Particles bounce off boundaries
    - 'shrink': Particles are moved to the boundary
    - 'random': Particles are randomly repositioned within bounds
    - 'intermediate': Particles are placed at random positions between current and boundary
    """

    velocity_strategy: Literal["unmodified", "adjust", "invert", "zero"] = "unmodified"
    """Strategy for handling velocities of out-of-bounds particles.

    - 'unmodified': Keep original velocity
    - 'adjust': Scale velocity to stay within bounds
    - 'invert': Reverse velocity direction
    - 'zero': Set velocity to zero
    """

    velocity_clamp_min: float | None = None
    """Minimum velocity value for clamping. Set to None to disable."""

    velocity_clamp_max: float | None = None
    """Maximum velocity value for clamping. Set to None to disable."""

    n_processes: PositiveInt | None = None
    """Number of processes for parallel function evaluation.

    Set to None to disable parallelization. When enabled, function evaluations
    for different particles are computed in parallel.
    """

    center_init: PositiveFloat = 1.0
    """Scaling factor for initial particle positions.

    This parameter affects the initial distribution of particles around the
    center of the search space.
    """

    static_topology: bool = False
    """Whether to use a static topology that doesn't change during optimization.

    If True, the neighborhood structure remains fixed throughout the optimization.
    If False, the neighborhood can be updated dynamically based on particle positions.
    """

    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_PYSWARMS_INSTALLED:
            raise NotInstalledError(PYSWARMS_NOT_INSTALLED_ERROR)

        import pyswarms as ps

        # Build structured options using dataclass
        pso_options = LocalBestPSOOptions(
            cognitive_parameter=self.cognitive_parameter,
            social_parameter=self.social_parameter,
            inertia_weight=self.inertia_weight,
            k_neighbors=self.k_neighbors,
            p_norm=self.p_norm,
        )
        options = _build_pso_options_dict(pso_options)

        # Use helper method for velocity clamping
        velocity_clamp = _build_velocity_clamp(
            self.velocity_clamp_min, self.velocity_clamp_max
        )

        bounds = _convert_bounds_to_pyswarms(problem.bounds)
        optimizer = ps.single.LocalBestPSO(
            n_particles=self.n_particles,
            dimensions=len(x0),
            options=options,
            bounds=bounds,
            bh_strategy=self.boundary_strategy,
            velocity_clamp=velocity_clamp,
            vh_strategy=self.velocity_strategy,
            center=self.center_init,
            ftol=self.convergence_ftol_rel,
            ftol_iter=self.convergence_ftol_iter,
            init_pos=None,
            static=self.static_topology,
        )

        objective_wrapper = _create_objective_wrapper(problem)

        result = optimizer.optimize(
            objective_func=objective_wrapper,
            iters=self.stopping_maxiter,
            n_processes=self.n_processes,
            verbose=False,
        )

        return _process_pyswarms_result(result)


@mark.minimizer(
    name="pyswarms_general",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_PYSWARMS_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    needs_bounds=False,
    supports_parallelism=True,
    supports_bounds=True,
    supports_infinite_bounds=False,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,
    disable_history=True,
)
@dataclass(frozen=True)
class PySwarmsGeneralPSO(Algorithm):
    r"""Minimize a scalar function using General Particle Swarm Optimization with custom topologies.

    This algorithm provides a flexible PSO implementation that supports different
    neighborhood topologies including Star, Ring, Von Neumann, and Random topologies.
    The choice of topology significantly affects the balance between exploration and
    exploitation in the optimization process.

    The velocity update follows the standard PSO equation:

    .. math::

        v_{ij}(t+1) = w \cdot v_{ij}(t) + c_1 r_{1j}(t)[y_{ij}(t) - x_{ij}(t)]
                      + c_2 r_{2j}(t)[\hat{y}_{nj}(t) - x_{ij}(t)]

    where :math:`\hat{y}_{nj}(t)` is the best position in the neighborhood defined
    by the selected topology.

    **Topology Options:**

    - **Star**: All particles connected to global best (equivalent to global best PSO)
    - **Ring**: Particles arranged in a ring, each connected to k neighbors
    - **Von Neumann**: 2D grid topology with connections to adjacent particles
    - **Random**: Random connections between particles, updated dynamically

    Different topologies provide different convergence characteristics:
    - Star topology: Fast convergence but prone to premature convergence
    - Ring topology: Balanced exploration/exploitation with slower information spread
    - Von Neumann: Good for maintaining diversity on multimodal problems
    - Random topology: Dynamic neighborhood structure for enhanced exploration

    This implementation uses PySwarms' GeneralOptimizerPSO which provides the most
    flexibility in configuring the swarm behavior.

    References:
        Kennedy, J., & Mendes, R. (2002). Population structure and particle swarm
        performance. Proceedings of the 2002 Congress on Evolutionary Computation
        (Vol. 2, pp. 1671-1676).

    """

    n_particles: PositiveInt = 50
    """Number of particles in the swarm."""

    cognitive_parameter: PositiveFloat = 0.5
    r"""Cognitive parameter :math:`c_1` controlling attraction to personal best."""

    social_parameter: PositiveFloat = 0.3
    r"""Social parameter :math:`c_2` controlling attraction to neighborhood best."""

    inertia_weight: PositiveFloat = 0.9
    r"""Inertia weight :math:`w` controlling momentum of particle movement."""

    topology_type: Literal["star", "ring", "vonneumann", "random"] = "star"
    """Topology type defining the neighborhood structure.

    - 'star': All particles connected to global best (fast convergence)
    - 'ring': Ring topology with k neighbors (balanced exploration/exploitation)
    - 'vonneumann': 2D grid topology (good diversity maintenance)
    - 'random': Random dynamic connections (enhanced exploration)
    """

    k_neighbors: PositiveInt = 3
    """Number of neighbors for ring, vonneumann, and random topologies.

    Only used when topology_type is not 'star'. Defines the neighborhood size
    for each particle.
    """

    p_norm: Literal[1, 2] = 2
    """Minkowski p-norm for distance calculation in neighborhood selection.

    Used by ring, vonneumann, and random topologies for determining neighbors.
    """

    vonneumann_range: PositiveInt = 1
    r"""Range parameter :math:`r` for Von Neumann topology.

    Defines the range of connections in the 2D grid. Higher values create
    larger neighborhoods but may reduce the benefits of the grid structure.
    Only used when topology_type is 'vonneumann'.
    """

    convergence_ftol_rel: NonNegativeFloat = -np.inf
    r"""Relative tolerance for convergence based on function value changes.

    The algorithm converges when the relative change in the best function value
    over the last ``convergence_ftol_iter`` iterations is below this threshold.
    Set to -np.inf to disable this convergence criterion.
    """

    convergence_ftol_iter: PositiveInt = 1
    """Number of iterations to check for function value convergence.

    The algorithm checks convergence over this many recent iterations.
    """

    stopping_maxiter: PositiveInt = STOPPING_MAXITER
    """Maximum number of iterations before stopping."""

    stopping_maxfun: PositiveInt = STOPPING_MAXFUN_GLOBAL
    """Maximum number of function evaluations before stopping.

    The default value is set lower than local optimizers since global optimizers
    typically require more function evaluations.
    """

    boundary_strategy: Literal["periodic", "reflective", "shrink", "random", "intermediate"] = "periodic"
    """Strategy for handling particles that move outside the bounds.

    - 'periodic': Particles wrap around to the opposite boundary
    - 'reflective': Particles bounce off boundaries
    - 'shrink': Particles are moved to the boundary
    - 'random': Particles are randomly repositioned within bounds
    - 'intermediate': Particles are placed at random positions between current and boundary
    """

    velocity_strategy: Literal["unmodified", "adjust", "invert", "zero"] = "unmodified"
    """Strategy for handling velocities of out-of-bounds particles.

    - 'unmodified': Keep original velocity
    - 'adjust': Scale velocity to stay within bounds
    - 'invert': Reverse velocity direction
    - 'zero': Set velocity to zero
    """

    velocity_clamp_min: float | None = None
    """Minimum velocity value for clamping. Set to None to disable."""

    velocity_clamp_max: float | None = None
    """Maximum velocity value for clamping. Set to None to disable."""

    n_processes: PositiveInt | None = None
    """Number of processes for parallel function evaluation.

    Set to None to disable parallelization. When enabled, function evaluations
    for different particles are computed in parallel.
    """

    center_init: PositiveFloat = 1.0
    """Scaling factor for initial particle positions.

    This parameter affects the initial distribution of particles around the
    center of the search space.
    """

    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_PYSWARMS_INSTALLED:
            raise NotInstalledError(PYSWARMS_NOT_INSTALLED_ERROR)

        import pyswarms as ps

        # Build structured options using dataclass
        k_neighbors = self.k_neighbors if self.topology_type in ["ring", "vonneumann", "random"] else None
        p_norm = self.p_norm if self.topology_type in ["ring", "vonneumann", "random"] else None
        vonneumann_range = self.vonneumann_range if self.topology_type == "vonneumann" else None

        pso_options = GeneralPSOOptions(
            cognitive_parameter=self.cognitive_parameter,
            social_parameter=self.social_parameter,
            inertia_weight=self.inertia_weight,
            k_neighbors=k_neighbors,
            p_norm=p_norm,
            vonneumann_range=vonneumann_range,
        )
        options = _build_pso_options_dict(pso_options)

        # Use helper method for topology creation
        topology = _create_topology_instance(self.topology_type)

        # Use helper method for velocity clamping
        velocity_clamp = _build_velocity_clamp(
            self.velocity_clamp_min, self.velocity_clamp_max
        )

        bounds = _convert_bounds_to_pyswarms(problem.bounds)
        optimizer = ps.single.GeneralOptimizerPSO(
            n_particles=self.n_particles,
            dimensions=len(x0),
            options=options,
            topology=topology,
            bounds=bounds,
            bh_strategy=self.boundary_strategy,
            velocity_clamp=velocity_clamp,
            vh_strategy=self.velocity_strategy,
            center=self.center_init,
            ftol=self.convergence_ftol_rel,
            ftol_iter=self.convergence_ftol_iter,
            init_pos=None,
        )

        objective_wrapper = _create_objective_wrapper(problem)

        result = optimizer.optimize(
            objective_func=objective_wrapper,
            iters=self.stopping_maxiter,
            n_processes=self.n_processes,
            verbose=False,
        )

        return _process_pyswarms_result(result)
