{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Numerical differentiation\n", "\n", "In this tutorial, you will learn how to numerically differentiate functions with\n", "optimagic."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "import optimagic as om"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic usage of `first_derivative`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fun(params):\n", "    return params @ params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = om.first_derivative(\n", "    func=fun,\n", "    params=np.arange(5),\n", ")\n", "\n", "fd.derivative"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic usage of `second_derivative`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sd = om.second_derivative(\n", "    func=fun,\n", "    params=np.arange(5),\n", ")\n", "\n", "sd.derivative.round(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## You can parallelize"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = om.first_derivative(\n", "    func=fun,\n", "    params=np.arange(5),\n", "    n_cores=4,\n", ")\n", "\n", "fd.derivative"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sd = om.second_derivative(\n", "    func=fun,\n", "    params=np.arange(5),\n", "    n_cores=4,\n", ")\n", "\n", "sd.derivative.round(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `params` do not have to be vectors\n", "\n", "In optimagic, params can be arbitrary [pytrees](https://jax.readthedocs.io/en/latest/pytrees.html). Examples are (nested) dictionaries of numbers, arrays and pandas objects. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def dict_fun(params):\n", "    return params[\"a\"] ** 2 + params[\"b\"] ** 2 + (params[\"c\"] ** 2).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = om.first_derivative(\n", "    func=dict_fun,\n", "    params={\"a\": 0, \"b\": 1, \"c\": pd.Series([2, 3, 4])},\n", ")\n", "\n", "fd.derivative"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Description of the output\n", "\n", "> Note. Understanding the output of the first and second derivative requires terminolgy\n", "> of pytrees. Please refer to the\n", "> [JAX documentation of pytrees](https://jax.readthedocs.io/en/latest/pytrees.html).\n", "\n", "The output tree of `first_derivative` has the same structure as the params tree.\n", "Equivalent to the 1-d numpy array case, where the gradient is a vector of shape\n", "`(len(params),)`. If, however, the params tree contains non-scalar entries like\n", "`numpy.ndarray`'s, `pandas.Series`', or `pandas.DataFrame`'s, the output is not expanded\n", "but a block is created instead. In the above example, the entry `params[\"c\"]` is a\n", "`pandas.Series` with 3 entries. Thus, the first derivative output contains the\n", "corresponding 3x1-block of the gradient at the position `[\"c\"]`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd.derivative[\"c\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sd = om.second_derivative(\n", "    func=dict_fun,\n", "    params={\"a\": 0, \"b\": 1, \"c\": pd.Series([2, 3, 4])},\n", ")\n", "\n", "sd.derivative"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Description of the output\n", "\n", "> Note. Understanding the output of the first and second derivative requires terminolgy\n", "> of pytrees. Please refer to the\n", "> [JAX documentation of pytrees](https://jax.readthedocs.io/en/latest/pytrees.html).\n", "\n", "The output of `second_derivative` when using a general pytrees looks more complex but\n", "is easy once we remember that the second derivative is equivalent to applying the first\n", "derivative twice.\n", "\n", "The output tree is a product of the params tree with itself. This is equivalent to the\n", "1-d numpy array case, where the hessian is a matrix of shape\n", "`(len(params), len(params))`. If, however, the params tree contains non-scalar entries\n", "like `numpy.ndarray`'s, `pandas.Series`', or `pandas.DataFrame`'s, the output is not\n", "expanded but a block is created instead. In the above example, the entry `params[\"c\"]`\n", "is a 3-dimensional `pandas.Series`. Thus, the second derivative output contains the\n", "corresponding 3x3-block of the hessian at the position `[\"c\"][\"c\"]`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sd.derivative[\"c\"][\"c\"].round(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## There are many options\n", "\n", "You can choose which finite difference method to use, whether we should respect\n", "parameter bounds, or whether to evaluate the function in parallel. Let's go through\n", "some basic examples. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## You can choose the difference method\n", "\n", "> Note. A mathematical explanation of the background of the difference methods can be\n", "> found on the corresponding [explanation page](../explanation/numdiff_background.md)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fd = om.first_derivative(\n", "    func=fun,\n", "    params=np.arange(5),\n", "    method=\"backward\",  # default: 'central'\n", ")\n", "\n", "fd.derivative"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sd = om.second_derivative(\n", "    func=fun,\n", "    params=np.arange(5),\n", "    method=\"forward\",  # default: 'central_cross'\n", ")\n", "\n", "sd.derivative.round(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## You can add bounds  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["params = np.arange(5)\n", "\n", "fd = om.first_derivative(\n", "    func=fun,\n", "    params=params,\n", "    # forces first_derivative to use forward differences\n", "    bounds=om.Bounds(lower=params, upper=params + 1),\n", ")\n", "\n", "fd.derivative"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Of course, bounds also work in `second_derivative`."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}, "vscode": {"interpreter": {"hash": "40d3a090f54c6569ab1632332b64b2c03c39dcf918b08424e98f38b5ae0af88f"}}}, "nbformat": 4, "nbformat_minor": 4}