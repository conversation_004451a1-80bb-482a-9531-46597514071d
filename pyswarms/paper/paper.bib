@inproceedings{kennedyIJCNN1995,
  author    = {<PERSON> and <PERSON>},
  title     = {Particle Swarm Optimization},
  booktitle = {Proceedings of the IEEE International Joint Conference on Neural Networks},
  year      = {1995},
  month     = {nov},
  OPTpages  = {1942--1948}
}

@inproceedings{kennedyMHS1995,
  author    = {<PERSON> and <PERSON>},
  title     = {A New Optimizer for Particle Swarm Theory},
  booktitle = {Proceedings of the Sixth International Symposium on Micromachine and Human Science},
  year      = {1995},
  OPTpages  = {39--43}
}

@inproceedings{kennedySMC1997,
  author    = {<PERSON> and <PERSON>},
  title     = {A discrete binary particle swarm optimization algorithm},
  booktitle = {Proceedings of the IEEE International Conference on Systems, Man, and Cybernetics},
  year      = {1997}
}

@article{deapJMLR2012, 
    author      = {F\'elix-<PERSON> and <PERSON>\c{c}ois-<PERSON> {<PERSON>} and <PERSON><PERSON><PERSON>\'<PERSON> and <PERSON> and <PERSON>\'e },
    title       = {{DEAP}: Evolutionary Algorithms Made Easy},
    pages       = {2171--2175},
    volume      = {13},
    month       = {jul},
    year        = {2012},
    journal     = {Journal of Machine Learning Research}
}

@misc{pagmo2017,
    author      = {Francesco <PERSON>iscani and <PERSON>io Izzo and <PERSON>\"{a}rtens},
    title       = {esa/pagmo2: pagmo 2.6},
    month       = {nov},
    year        = {2017},
    doi         = {10.5281/zenodo.1054110},
    url         = {https://doi.org/10.5281/zenodo.1054110}
}

@article{numpycse,
    author      = {Stefan van der Walt and S. Chris Colbert and Gael Varoquaux},
    title       = {The NumPy Array: A Structure for Efficient Numerical Computation},
    journal     = {Computing in Science \& Engineering},
    volume      = {13},
    issue       = {2},
    pages       = {22--30},
    doi         = {10.1109/MCSE.2011.37},
    year        = {2011},
    month       = {mar}
}

@Misc{scipyweb,
    author      = {Eric Jones and Travis Oliphant and Pearu Peterson and others},
    title       = {{SciPy}: Open source scientific tools for {Python}},
    year        = {2001--},
    url         = {http://www.scipy.org/},
}

@article{matplotlibcse,
    author      = {J.D. Hunter},
    title       = {Matplotlib: A 2D Graphics Environment},
    journal     = {Computing in Science \& Engineering},
    volume      = {9},
    issue       = {3},
    year        = {2007},
    month       = {may},
    pages       = {90--95},
    doi         = {10.1109/MCSE.2007.55}
}