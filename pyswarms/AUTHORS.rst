=======
Credits
=======

This project was inspired by the pyswarm_ module that performs PSO with constrained support.
The package was created with <PERSON><PERSON><PERSON><PERSON>_ and the `audreyr/cookiecutter-pypackage`_ project template.

.. _pyswarm: https://github.com/tisimst/pyswarm
.. _Cookiecutter: https://github.com/audreyr/cookiecutter
.. _`audreyr/cookiecutter-pypackage`: https://github.com/audreyr/cookiecutter-pypackage

Maintainers
-----------

* <PERSON> (`@ljvmiranda921`_)
* <PERSON> (`@whzup`_)
* <PERSON><PERSON><PERSON><PERSON> (`@SioKCronin`_)

Contributors
------------

* <PERSON><PERSON><PERSON> (`@<PERSON>-<PERSON>`_)
* <PERSON> (`@jazcap53`_)
* Charalampos Papadimitriou (`@CPapadim`_)
* <PERSON><PERSON> (`@mamadyonline`_)
* <PERSON> (`@slek120`_)
* <PERSON> (`@jayspeidell`_)
* <PERSON><PERSON>oward (`@bradahoward`_)
* <PERSON> (`@ThomasCES`_)

.. _`@ljvmiranda921`: https://github.com/ljvmiranda921
.. _`@Carl-K`: https://github.com/Carl-K
.. _`@SioKCronin`: https://github.com/SioKCronin
.. _`@jazcap53`: https://github.com/jazcap53
.. _`@CPapadim`: https://github.com/CPapadim
.. _`@mamadyonline`: https://github.com/mamadyonline
.. _`@slek120`: https://github.com/slek120
.. _`@whzup`: https://github.com/whzup
.. _`@jayspeidell`: https://github.com/jayspeidell
.. _`@bradahoward`: https://github.com/bradahoward
.. _`@ThomasCES`: https://github.com/ThomasCES
