alabaster==0.7.12
attrs==18.1.0
babel==2.6.0
backcall==0.1.0
bleach==3.1.0
bumpversion==0.5.3
certifi==2018.11.29
chardet==3.0.4
coverage==4.5.1
cycler==0.10.0
decorator==4.4.0
defusedxml==0.6.0
docutils==0.14
entrypoints==0.3
flake8==3.5.0
future==0.16.0
idna==2.8
imagesize==1.1.0
iniconfig==1.1.1
ipykernel==5.1.1
ipython-genutils==0.2.0
ipython==7.5.0
jedi==0.13.3
jinja2==2.10
joblib==0.13.2
jsonschema==3.0.1
jupyter-client==5.2.4
jupyter-core==4.4.0
kiwisolver==1.0.1
markupsafe==1.1.0
matplotlib==3.0.2
mccabe==0.6.1
mistune==0.8.4
mock==2.0.0
nbconvert==5.5.0
nbformat==4.4.0
nbsphinx==0.4.2
nbstripout==0.3.5
numpy==1.16.1
packaging==19.0
pandas==0.24.2
pandocfilters==1.4.2
parso==0.4.0
pbr==5.1.1
pexpect==4.7.0
pickleshare==0.7.5
pluggy==0.13.1
pockets==0.7.2
prompt-toolkit==2.0.9
ptyprocess==0.6.0
py==1.9.0
pycodestyle==2.3.1
pyflakes==1.6.0
pygments==2.3.1
pyparsing==2.3.1
pyrsistent==0.15.2
pytest-cov==2.10.1
pytest==6.1.2
python-dateutil==2.7.5
pytz==2018.9
pyyaml==3.13
pyzmq==18.0.1
requests==2.21.0
scikit-learn==0.21.1
scipy==1.2.0
seaborn==0.9.0
six==1.12.0
snowballstemmer==1.2.1
sphinx-rtd-theme==0.4.3
sphinx>=1.8,<2
sphinxcontrib-napoleon==0.7
sphinxcontrib-websupport==1.1.0
testpath==0.4.2
toml==0.10.2
tornado==6.0.2
tox==3.2.1
tqdm==4.24.0
traitlets==4.3.2
urllib3==1.24.1
virtualenv==16.3.0
wcwidth==0.1.7
webencodings==0.5.1
wheel==0.31.1

# The following packages are considered to be unsafe in a requirements file:
# setuptools
