# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Ipython notebooks
.ipynb_checkpoints

# C extensions
*.so

# Other not important stuff
.vscode

# Distribution / packaging
.Python
venv/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log

# Sphinx documentation
docs/_build/
None0000000.png

# PyBuilder
target/

# pyenv python configuration file
.python-version

# Vagrant folder
.vagrant
