[tox]
envlist = py35, py36, py37, flake8

[testenv:flake8]
basepython=python
deps=-rrequirements-dev.txt
commands=python -m flake8 pyswarms

[testenv]
setenv =
    PYTHONPATH = {toxinidir}
deps=-rrequirements-dev.txt
commands = python -m pytest tests --junitxml=junit/test-results.xml

; If you want to make tox run the tests with the same versions, create a
; requirements.txt with the pinned versions and uncomment the following lines:
; deps =
;     -r{toxinidir}/requirements.txt
