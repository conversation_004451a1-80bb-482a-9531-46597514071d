pyswarms.backend package
=========================

You can import all the native helper methods in this package
using the command:

.. code-block:: python

    import pyswarms.backend as P

Then call the methods found in each module. Note that these methods interface
with the Swarm class provided in the :mod:`pyswarms.backend.swarms` module.

pyswarms.backend.generators module
----------------------------------

.. automodule:: pyswarms.backend.generators
   :members:

pyswarms.backend.handlers module
--------------------------------

.. automodule:: pyswarms.backend.handlers
   :members:

pyswarms.backend.operators module
----------------------------------

.. automodule:: pyswarms.backend.operators
   :members:

