pyswarms.topology package
=========================

This package implements various swarm topologies that may be useful as you
build your own swarm implementations. Each topology can perform the
following:

* Determine the best particle on a given swarm.
* Compute the next position given a current swarm position.
* Compute the velocities given a swarm configuration.

pyswarms.backend.topology.base module
--------------------------------------

.. automodule:: pyswarms.backend.topology.base
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

pyswarms.backend.topology.star module
--------------------------------------

.. automodule:: pyswarms.backend.topology.star
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

pyswarms.backend.topology.ring module
--------------------------------------

.. automodule:: pyswarms.backend.topology.ring
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

pyswarms.backend.topology.von_neumann module
--------------------------------------

.. automodule:: pyswarms.backend.topology.von_neumann
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

pyswarms.backend.topology.pyramid module
--------------------------------------

.. automodule:: pyswarms.backend.topology.pyramid
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

pyswarms.backend.topology.random module
--------------------------------------

.. automodule:: pyswarms.backend.topology.random
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__
