pyswarms.handlers package
=========================

This package implements different handling strategies for the optimiziation.
The :class:`<PERSON>undaryHandler` and the
:class:`VelocityHandler` handlers help avoiding that particles
leave the defined search space. 
The :class:`Op<PERSON>Handler` helps in varying the options with time/iterations.

pyswarms.handlers class
-----------------------

.. automodule:: pyswarms.backend.handlers
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__, __call__
