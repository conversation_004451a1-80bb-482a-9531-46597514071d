{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solving an electric circuit using Particle Swarm Optimization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction\n", "\n", "PSO can be utilized in a wide variety of fields. In this example, the problem consists of analysing a given electric circuit and finding the electric current that flows through it. To accomplish this, the ```pyswarms``` library will be used to solve a non-linear equation by restructuring it as an optimization problem. The circuit is composed by a source, a resistor and a diode, as shown below.\n", "\n", "![Circuit](https://user-images.githubusercontent.com/39431903/43938822-29aaf9b8-9c66-11e8-8e54-01530db005c6.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Mathematical Formulation\n", "\n", "<PERSON><PERSON><PERSON>'s voltage law states that the directed sum of the voltages around any closed loop is zero. In other words, the sum of the voltages of the passive elements must be equal to the sum of the voltages of the active elements, as expressed by the following equation:\n", "\n", "$U = v_D + v_R $, where $U$ represents the voltage of the source and, $v_D$ and $v_R$ represent the voltage of the diode and the resistor, respectively.\n", "\n", "To determine the current flowing through the circuit, $v_D$ and $v_R$ need to be defined as functions of $I$. A simplified <PERSON>ley equation will be used to formulate the current-voltage characteristic function of the diode. This function relates the current that flows through the diode with the voltage across it. Both $I_s$ and $v_T$ are known properties.\n", "\n", "$I = I_s  e^{\\frac{v_D}{v_T}}$\n", "\n", "Where:\n", "\n", "- $I$ : diode current\n", "- $I_s$ : reverse bias saturation current\n", "- $v_D$ : diode voltage\n", "- $v_T$ : thermal voltage\n", "\n", "Which can be formulated over $v_D$:\n", "\n", "$v_D = v_T \\log{\\left |\\frac{I}{I_s}\\right |}$\n", "\n", "The voltage over the resistor can be written as a function of the resistor's resistance $R$ and the current $I$:\n", "\n", "$v_R = R  I$\n", "\n", "And by replacing these expressions on the <PERSON><PERSON><PERSON>'s voltage law equation, the following equation is obtained:\n", "\n", "$U = v_T \\log{\\left |\\frac{I}{I_s}\\right |} + R I$\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To find the solution of the problem, the previous equation needs to be solved for $I$, which is the same as finding $I$ such that the cost function $c$ equals zero, as shown below. By doing this, solving for $I$ is restructured as a minimization problem. The absolute value is necessary because we don't want to obtain negative currents.\n", "\n", "$c = \\left | U - v_T \\log{\\left | \\frac{I}{I_s} \\right |} - RI \\right |$\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Known parameter values\n", "\n", "The voltage of the source is $10 \\space V$ and the resistance of the resistor is $100 \\space \\Omega$. The diode is a silicon diode and it is assumed to be at room temperature.\n", "\n", "$U = 10 \\space V$\n", "\n", "$R = 100 \\space \\Omega$\n", "\n", "$I_s = 9.4 \\space pA = 9.4 \\times 10^{-12} \\space A$ (reverse bias saturation current of silicon diodes at room temperature, $T=300 \\space K$)\n", "\n", "$v_T = 25.85 \\space mV = 25.85 \\times 10^{-3} \\space V$ (thermal voltage at room temperature, $T=300 \\space K$)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optimization"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import modules\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Import PySwarms\n", "import pyswarms as ps"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running on Python version: 3.6.8 |Anaconda, Inc.| (default, Dec 30 2018, 01:22:34) \n", "[GCC 7.3.0]\n"]}], "source": ["print('Running on Python version: {}'.format(sys.version))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining the cost fuction\n", "\n", "The first argument of the cost function is a ```numpy.ndarray```. Each dimension of this array represents an unknown variable. In this problem, the unknown variable is just $I$, thus the first argument is a unidimensional array. As default, the thermal voltage is assumed to be $25.85 \\space mV$."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def cost_function(I):\n", "    \n", "    #Fixed parameters\n", "    U = 10\n", "    R = 100\n", "    I_s = 9.4e-12\n", "    v_t = 25.85e-3\n", "    \n", "    c = abs(U - v_t * np.log(abs(I[:, 0] / I_s)) - R * I[:, 0])\n", "    \n", "    return c  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting the optimizer\n", "\n", "To solve this problem, the global-best optimizer is going to be used. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2019-05-18 15:41:35,474 - pyswarms.single.global_best - INFO - Optimize for 30 iters with {'c1': 0.5, 'c2': 0.3, 'w': 0.3}\n", "pyswarms.single.global_best: 100%|██████████|30/30, best_cost=14 \n", "2019-05-18 15:41:35,863 - pyswarms.single.global_best - INFO - Optimization finished | best cost: 14.045352861989468, best pos: [0.23426529]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CPU times: user 111 ms, sys: 12.1 ms, total: 123 ms\n", "Wall time: 411 ms\n"]}], "source": ["%%time\n", "# Set-up hyperparameters\n", "options = {'c1': 0.5, 'c2': 0.3, 'w':0.3}\n", "\n", "# Call instance of PSO\n", "optimizer = ps.single.GlobalBestPSO(n_particles=10, dimensions=1, options=options)\n", "\n", "# Perform optimization\n", "cost, pos = optimizer.optimize(cost_function, iters=30)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.23426529444241187\n"]}], "source": ["print(pos[0])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14.045352861989468\n"]}], "source": ["print(cost)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Checking the solution\n", "\n", "The current flowing through the circuit is approximately $0.094 \\space A$ which yields a cost of almost zero. The graph below illustrates the relationship between the cost $c$ and the current $I$. As shown, the cost reaches its minimum value of zero when $I$ is somewhere close to $0.09$.\n", "\n", "The use of ```reshape(100, 1)``` is required since ```np.linspace(0.001, 0.1, 100)``` returns an array with shape ```(100,)``` and first argument of the cost function must be a unidimensional array, that is, an array with shape ```(100, 1)```. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["x = np.linspace(0.001, 0.1, 100).reshape(100, 1)\n", "y = cost_function(x)\n", "\n", "plt.plot(x, y)\n", "plt.xlabel('Current I [A]')\n", "plt.ylabel('Cost');"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another way of solving non-linear equations is by using non-linear solvers implemented in libraries such as ```scipy```. There are different solvers that one can choose which correspond to different numerical methods. We are going to use ```fsolve```, which is a general non-linear solver that finds the root of a given function. \n", "\n", "Unlike ```pyswarms```, the function (in this case, the cost function) to be used in ```fsolve``` must have as first argument a single value. Moreover, numerical methods need an initial guess for the solution, which can be made from the graph above. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Import non-linear solver\n", "from scipy.optimize import fsolve"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.09404768643017938\n"]}], "source": ["c = lambda I: abs(10 - 25.85e-3 * np.log(abs(I / 9.4e-12)) - 100 * I)\n", "\n", "initial_guess = 0.09\n", "\n", "current_I = fsolve(func=c, x0=initial_guess)\n", "\n", "print(current_I[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The best solution value found using the PSO method was approximately the same as the one found using a non-linear solver, about $0.094 \\space A$. In fact, the relative error was less than $1 \\times 10^{-5}$."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}