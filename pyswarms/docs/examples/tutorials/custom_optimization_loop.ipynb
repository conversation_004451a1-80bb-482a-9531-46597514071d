{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Writing your own optimization loop\n", "\n", "In this example, we will use the `pyswarms.backend` module to write our own optimization loop. We will try to recreate the Global best PSO using the native backend in PySwarms. Hopefully, this short tutorial can give you an idea on how to use this for your own custom swarm implementation. The idea is simple, again, let's refer to this diagram:\n", "\n", "![Optimization loop](optimization_loop.png)\n", "\n", "\n", "Some things to note:\n", "- Initialize a `Swarm` class and update its attributes for every iteration.\n", "- Initialize a `Topology` class (in this case, we'll use a `Star` topology), and use its methods to operate on the Swarm.\n", "- We can also use some additional methods in `pyswarms.backend` depending on our needs.\n", "\n", "Thus, for each iteration:\n", "1. We take an attribute from the `Swarm` class.\n", "2. Operate on it according to our custom algorithm with the help of the `Topology` class; and\n", "3. Update the `Swarm` class with the new attributes."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import modules\n", "import numpy as np\n", "\n", "# Import sphere function as objective function\n", "from pyswarms.utils.functions.single_obj import sphere as f\n", "\n", "# Import backend modules\n", "import pyswarms.backend as P\n", "from pyswarms.backend.topology import Star\n", "\n", "# Some more magic so that the notebook will reload external python modules;\n", "# see http://stackoverflow.com/questions/1907993/autoreload-of-modules-in-ipython\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Native global-best PSO implementation\n", "\n", "Now, the global best PSO pseudocode looks like the following (adapted from [<PERSON><PERSON>, \"Computational Intelligence: An Introduction, 2002](https://www.wiley.com/en-us/Computational+Intelligence%3A+An+Introduction%2C+2nd+Edition-p-9780470035610)):\n", "\n", "```python\n", "# Python-version of gbest algorithm from <PERSON><PERSON><PERSON>'s book\n", "for i in range(iterations):\n", "    for particle in swarm:\n", "        # Part 1: If current position is less than the personal best,\n", "        if f(current_position[particle]) < f(personal_best[particle]):\n", "            # Update personal best\n", "            personal_best[particle] = current_position[particle]\n", "        # Part 2: If personal best is less than global best,\n", "        if f(personal_best[particle]) < f(global_best):\n", "            # Update global best\n", "            global_best = personal_best[particle]\n", "        # Part 3: Update velocity and position matrices\n", "        update_velocity()\n", "        update_position()\n", "```\n", "\n", "As you can see, the standard PSO has a three-part scheme: update the personal best, update the global best, and update the velocity and position matrices. We'll follow this three part scheme in our native implementation using the PySwarms backend\n", "\n", "Let's make a 2-dimensional swarm with 50 particles that will optimize the sphere function. First, let's initialize the important attributes in our algorithm"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The following are the attributes of our swarm: dict_keys(['position', 'velocity', 'n_particles', 'dimensions', 'options', 'pbest_pos', 'best_pos', 'pbest_cost', 'best_cost', 'current_cost'])\n"]}], "source": ["my_topology = Star() # The Topology Class\n", "my_options = {'c1': 0.6, 'c2': 0.3, 'w': 0.4} # arbitrarily set\n", "my_swarm = P.create_swarm(n_particles=50, dimensions=2, options=my_options) # The Swarm Class\n", "\n", "print('The following are the attributes of our swarm: {}'.format(my_swarm.__dict__.keys()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's write our optimization loop!"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Iteration: 1 | my_swarm.best_cost: 0.0009\n", "Iteration: 21 | my_swarm.best_cost: 0.0000\n", "Iteration: 41 | my_swarm.best_cost: 0.0000\n", "Iteration: 61 | my_swarm.best_cost: 0.0000\n", "Iteration: 81 | my_swarm.best_cost: 0.0000\n", "The best cost found by our swarm is: 0.0000\n", "The best position found by our swarm is: [3.30572365e-19 2.93696483e-19]\n"]}], "source": ["iterations = 100 # Set 100 iterations\n", "for i in range(iterations):\n", "    # Part 1: Update personal best\n", "    my_swarm.current_cost = f(my_swarm.position) # Compute current cost\n", "    my_swarm.pbest_cost = f(my_swarm.pbest_pos)  # Compute personal best pos\n", "    my_swarm.pbest_pos, my_swarm.pbest_cost = P.compute_pbest(my_swarm) # Update and store\n", "    \n", "    # Part 2: Update global best\n", "    # Note that gbest computation is dependent on your topology\n", "    if np.min(my_swarm.pbest_cost) < my_swarm.best_cost:\n", "        my_swarm.best_pos, my_swarm.best_cost = my_topology.compute_gbest(my_swarm)\n", "    \n", "    # Let's print our output\n", "    if i%20==0:\n", "        print('Iteration: {} | my_swarm.best_cost: {:.4f}'.format(i+1, my_swarm.best_cost))\n", "    \n", "    # Part 3: Update position and velocity matrices\n", "    # Note that position and velocity updates are dependent on your topology\n", "    my_swarm.velocity = my_topology.compute_velocity(my_swarm)\n", "    my_swarm.position = my_topology.compute_position(my_swarm)\n", "    \n", "print('The best cost found by our swarm is: {:.4f}'.format(my_swarm.best_cost))\n", "print('The best position found by our swarm is: {}'.format(my_swarm.best_pos))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Of course, we can just use the `GlobalBestPSO` implementation in PySwarms (it has boundary support, tolerance, initial positions, etc.):"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2019-05-18 15:39:20,737 - pyswarms.single.global_best - INFO - Optimize for 100 iters with {'c1': 0.6, 'c2': 0.3, 'w': 0.4}\n", "pyswarms.single.global_best: 100%|██████████|100/100, best_cost=0.00418\n", "2019-05-18 15:39:21,942 - pyswarms.single.global_best - INFO - Optimization finished | best cost: 0.004177699645933291, best pos: [0.03663518 0.05325001]\n"]}, {"data": {"text/plain": ["(0.004177699645933291, array([0.03663518, 0.05325001]))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyswarms.single import GlobalBestPSO\n", "\n", "optimizer = GlobalBestPSO(n_particles=50, dimensions=2, options=my_options) # Reuse our previous options\n", "optimizer.optimize(f, iters=100)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}