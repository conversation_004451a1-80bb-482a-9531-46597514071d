{"cells": [{"source": ["# Options Handler Tutorial\n", "Varying options with time is a well regarded technique in particle swarm optimization for faster convergence and\n", "better solutions. This class exposes methods to do the same.\n", "\n", "In this example, we will demonstrate some common variation techniques along with some [visualisation].\n", "- `oh_strategy`: a dictionary containing the strategies for each option\n", "- `end_opts`: a dictionary containing the ending options for each option\n", "- `plot_cost_history`: for plotting the cost history of a swarm given a matrix\n", "- `plot_contour`: for plotting swarm trajectories of a 2D-swarm in two-dimensional space\n", "\n", "[visualisation]: https://github.com/ljvmiranda921/pyswarms/blob/master/docs/examples/tutorials/visualization.ipynb\n"], "cell_type": "markdown", "metadata": {"collapsed": true, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 1, "outputs": [], "source": ["# Import modules\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from IPython.display import Image\n", "\n", "# Import PySwarms\n", "import pyswarms as ps\n", "from pyswarms.utils.functions import single_obj as fx\n", "from pyswarms.utils.plotters import (plot_cost_history, plot_contour)\n", "\n", "from pyswarms.backend.handlers import OptionsHandler"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"source": ["Let's create some optimizers for comparison. Here, we're going to use Global-best PSO to find the minima\n", "of a sphere function. As usual, we simply create an instance of its class `pyswarms.single.GlobalBestPSO`\n", "by passing the required parameters that we will use. Then, we'll call the `optimize()` method for 100 iterations for both and visualise the results"], "cell_type": "markdown", "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["2020-12-11 19:23:18,434 - pyswarms.single.global_best - INFO - Optimize for 100 iters with {'c1': 0.5, 'c2': 0.3, 'w': 0.9}\n", "pyswarms.single.global_best: 100%|██████████|100/100, best_cost=7.95e-10\n", "2020-12-11 19:23:18,540 - pyswarms.single.global_best - INFO - Optimization finished | best cost: 7.946552028624106e-10, best pos: [ 2.74938126e-05 -6.22458616e-06]\n", "2020-12-11 19:23:18,541 - pyswarms.single.global_best - INFO - Optimize for 100 iters with {'c1': 0.5, 'c2': 0.3, 'w': 0.9}\n", "pyswarms.single.global_best: 100%|██████████|100/100, best_cost=7.99e-28\n", "2020-12-11 19:23:18,648 - pyswarms.single.global_best - INFO - Optimization finished | best cost: 7.987498732542365e-28, best pos: [ 7.14102116e-15 -2.73451219e-14]\n"]}], "source": ["options = {'c1':0.5, 'c2':0.3, 'w':0.9}  # starting options\n", "optimizer_without_handle=ps.single.GlobalBestPSO(n_particles=50, dimensions=2, options=options)\n", "optimizer_with_handle = ps.single.GlobalBestPSO(n_particles=50, dimensions=2, options=options, oh_strategy={\"w\":'exp_decay', 'c1':'lin_variation'})\n", "\n", "cost, pos = optimizer_without_handle.optimize(fx.sphere, iters=100)\n", "cost_h, pos_h = optimizer_with_handle.optimize(fx.sphere, iters=100)\n"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "source": ["## Comparing the cost history\n", "\n", "To plot the cost history, we simply obtain the `cost_history` from the `optimizer` class and pass it to the `cost_history` function. In addition, we can obtain the following histories from the same class:\n", "- mean_neighbor_history: average local best history of all neighbors throughout optimization\n", "- mean_pbest_history: average personal best of the particles throughout optimization"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 3, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 1080x504 with 2 Axes>", "image/svg+xml": "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Created with matplotlib (https://matplotlib.org/) -->\n<svg height=\"440.394375pt\" version=\"1.1\" viewBox=\"0 0 894.34375 440.394375\" width=\"894.34375pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n <metadata>\n  <rdf:RDF xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n   <cc:Work>\n    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n    <dc:date>2020-12-11T19:23:19.126084</dc:date>\n    <dc:format>image/svg+xml</dc:format>\n    <dc:creator>\n     <cc:Agent>\n      <dc:title>Matplotlib v3.3.3, https://matplotlib.org/</dc:title>\n     </cc:Agent>\n    </dc:creator>\n   </cc:Work>\n  </rdf:RDF>\n </metadata>\n <defs>\n  <style type=\"text/css\">*{stroke-linecap:butt;stroke-linejoin:round;}</style>\n </defs>\n <g id=\"figure_1\">\n  <g id=\"patch_1\">\n   <path d=\"M 0 440.394375 \nL 894.34375 440.394375 \nL 894.34375 0 \nL 0 0 \nz\n\" style=\"fill:none;\"/>\n  </g>\n  <g id=\"axes_1\">\n   <g id=\"patch_2\">\n    <path d=\"M 50.14375 402.838125 \nL 430.**********.838125 \nL 430.598295 22.318125 \nL 50.14375 22.318125 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_1\">\n    <g id=\"xtick_1\">\n     <g id=\"line2d_1\">\n      <defs>\n       <path d=\"M 0 0 \nL 0 3.5 \n\" id=\"m4c577bf323\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"67.437138\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_1\">\n      <!-- 0 -->\n      <g transform=\"translate(64.255888 417.436562)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 31.78125 66.40625 \nQ 24.171875 66.40625 20.328125 58.90625 \nQ 16.5 51.421875 16.5 36.375 \nQ 16.5 21.390625 20.328125 13.890625 \nQ 24.171875 6.390625 31.78125 6.390625 \nQ 39.453125 6.390625 43.28125 13.890625 \nQ 47.125 21.390625 47.125 36.375 \nQ 47.125 51.421875 43.28125 58.90625 \nQ 39.453125 66.40625 31.78125 66.40625 \nz\nM 31.78125 74.21875 \nQ 44.046875 74.21875 50.515625 64.515625 \nQ 56.984375 54.828125 56.984375 36.375 \nQ 56.984375 17.96875 50.515625 8.265625 \nQ 44.046875 -1.421875 31.78125 -1.421875 \nQ 19.53125 -1.421875 13.0625 8.265625 \nQ 6.59375 17.96875 6.59375 36.375 \nQ 6.59375 54.828125 13.0625 64.515625 \nQ 19.53125 74.21875 31.78125 74.21875 \nz\n\" id=\"DejaVuSans-48\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_2\">\n     <g id=\"line2d_2\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"137.309415\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_2\">\n      <!-- 20 -->\n      <g transform=\"translate(130.946915 417.436562)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 19.1875 8.296875 \nL 53.609375 8.296875 \nL 53.609375 0 \nL 7.328125 0 \nL 7.328125 8.296875 \nQ 12.9375 14.109375 22.625 23.890625 \nQ 32.328125 33.6875 34.8125 36.53125 \nQ 39.546875 41.84375 41.421875 45.53125 \nQ 43.3125 49.21875 43.3125 52.78125 \nQ 43.3125 58.59375 39.234375 62.25 \nQ 35.15625 65.921875 28.609375 65.921875 \nQ 23.96875 65.921875 18.8125 64.3125 \nQ 13.671875 62.703125 7.8125 59.421875 \nL 7.8125 69.390625 \nQ 13.765625 71.78125 18.9375 73 \nQ 24.125 74.21875 28.421875 74.21875 \nQ 39.75 74.21875 46.484375 68.546875 \nQ 53.21875 62.890625 53.21875 53.421875 \nQ 53.21875 48.921875 51.53125 44.890625 \nQ 49.859375 40.875 45.40625 35.40625 \nQ 44.1875 33.984375 37.640625 27.21875 \nQ 31.109375 20.453125 19.1875 8.296875 \nz\n\" id=\"DejaVuSans-50\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_3\">\n     <g id=\"line2d_3\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"207.181691\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_3\">\n      <!-- 40 -->\n      <g transform=\"translate(200.819191 417.436562)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 37.796875 64.3125 \nL 12.890625 25.390625 \nL 37.796875 25.390625 \nz\nM 35.203125 72.90625 \nL 47.609375 72.90625 \nL 47.609375 25.390625 \nL 58.015625 25.390625 \nL 58.015625 17.1875 \nL 47.609375 17.1875 \nL 47.609375 0 \nL 37.796875 0 \nL 37.796875 17.1875 \nL 4.890625 17.1875 \nL 4.890625 26.703125 \nz\n\" id=\"DejaVuSans-52\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-52\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_4\">\n     <g id=\"line2d_4\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"277.053968\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_4\">\n      <!-- 60 -->\n      <g transform=\"translate(270.691468 417.436562)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 33.015625 40.375 \nQ 26.375 40.375 22.484375 35.828125 \nQ 18.609375 31.296875 18.609375 23.390625 \nQ 18.609375 15.53125 22.484375 10.953125 \nQ 26.375 6.390625 33.015625 6.390625 \nQ 39.65625 6.390625 43.53125 10.953125 \nQ 47.40625 15.53125 47.40625 23.390625 \nQ 47.40625 31.296875 43.53125 35.828125 \nQ 39.65625 40.375 33.015625 40.375 \nz\nM 52.59375 71.296875 \nL 52.59375 62.3125 \nQ 48.875 64.0625 45.09375 64.984375 \nQ 41.3125 65.921875 37.59375 65.921875 \nQ 27.828125 65.921875 22.671875 59.328125 \nQ 17.53125 52.734375 16.796875 39.40625 \nQ 19.671875 43.65625 24.015625 45.921875 \nQ 28.375 48.1875 33.59375 48.1875 \nQ 44.578125 48.1875 50.953125 41.515625 \nQ 57.328125 34.859375 57.328125 23.390625 \nQ 57.328125 12.15625 50.6875 5.359375 \nQ 44.046875 -1.421875 33.015625 -1.421875 \nQ 20.359375 -1.421875 13.671875 8.265625 \nQ 6.984375 17.96875 6.984375 36.375 \nQ 6.984375 53.65625 15.1875 63.9375 \nQ 23.390625 74.21875 37.203125 74.21875 \nQ 40.921875 74.21875 44.703125 73.484375 \nQ 48.484375 72.75 52.59375 71.296875 \nz\n\" id=\"DejaVuSans-54\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_5\">\n     <g id=\"line2d_5\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"346.926244\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_5\">\n      <!-- 80 -->\n      <g transform=\"translate(340.563744 417.436562)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 31.78125 34.625 \nQ 24.75 34.625 20.71875 30.859375 \nQ 16.703125 27.09375 16.703125 20.515625 \nQ 16.703125 13.921875 20.71875 10.15625 \nQ 24.75 6.390625 31.78125 6.390625 \nQ 38.8125 6.390625 42.859375 10.171875 \nQ 46.921875 13.96875 46.921875 20.515625 \nQ 46.921875 27.09375 42.890625 30.859375 \nQ 38.875 34.625 31.78125 34.625 \nz\nM 21.921875 38.8125 \nQ 15.578125 40.375 12.03125 44.71875 \nQ 8.5 49.078125 8.5 55.328125 \nQ 8.5 64.0625 14.71875 69.140625 \nQ 20.953125 74.21875 31.78125 74.21875 \nQ 42.671875 74.21875 48.875 69.140625 \nQ 55.078125 64.0625 55.078125 55.328125 \nQ 55.078125 49.078125 51.53125 44.71875 \nQ 48 40.375 41.703125 38.8125 \nQ 48.828125 37.15625 52.796875 32.3125 \nQ 56.78125 27.484375 56.78125 20.515625 \nQ 56.78125 9.90625 50.3125 4.234375 \nQ 43.84375 -1.421875 31.78125 -1.421875 \nQ 19.734375 -1.421875 13.25 4.234375 \nQ 6.78125 9.90625 6.78125 20.515625 \nQ 6.78125 27.484375 10.78125 32.3125 \nQ 14.796875 37.15625 21.921875 38.8125 \nz\nM 18.3125 54.390625 \nQ 18.3125 48.734375 21.84375 45.5625 \nQ 25.390625 42.390625 31.78125 42.390625 \nQ 38.140625 42.390625 41.71875 45.5625 \nQ 45.3125 48.734375 45.3125 54.390625 \nQ 45.3125 60.0625 41.71875 63.234375 \nQ 38.140625 66.40625 31.78125 66.40625 \nQ 25.390625 66.40625 21.84375 63.234375 \nQ 18.3125 60.0625 18.3125 54.390625 \nz\n\" id=\"DejaVuSans-56\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_6\">\n     <g id=\"line2d_6\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"416.798521\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_6\">\n      <!-- 100 -->\n      <g transform=\"translate(407.254771 417.436562)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 12.40625 8.296875 \nL 28.515625 8.296875 \nL 28.515625 63.921875 \nL 10.984375 60.40625 \nL 10.984375 69.390625 \nL 28.421875 72.90625 \nL 38.28125 72.90625 \nL 38.28125 8.296875 \nL 54.390625 8.296875 \nL 54.390625 0 \nL 12.40625 0 \nz\n\" id=\"DejaVuSans-49\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_7\">\n     <!-- Iterations -->\n     <g transform=\"translate(216.557741 431.114687)scale(0.1 -0.1)\">\n      <defs>\n       <path d=\"M 9.8125 72.90625 \nL 19.671875 72.90625 \nL 19.671875 0 \nL 9.8125 0 \nz\n\" id=\"DejaVuSans-73\"/>\n       <path d=\"M 18.3125 70.21875 \nL 18.3125 54.6875 \nL 36.8125 54.6875 \nL 36.8125 47.703125 \nL 18.3125 47.703125 \nL 18.3125 18.015625 \nQ 18.3125 11.328125 20.140625 9.421875 \nQ 21.96875 7.515625 27.59375 7.515625 \nL 36.8125 7.515625 \nL 36.8125 0 \nL 27.59375 0 \nQ 17.1875 0 13.234375 3.875 \nQ 9.28125 7.765625 9.28125 18.015625 \nL 9.28125 47.703125 \nL 2.6875 47.703125 \nL 2.6875 54.6875 \nL 9.28125 54.6875 \nL 9.28125 70.21875 \nz\n\" id=\"DejaVuSans-116\"/>\n       <path d=\"M 56.203125 29.59375 \nL 56.203125 25.203125 \nL 14.890625 25.203125 \nQ 15.484375 15.921875 20.484375 11.0625 \nQ 25.484375 6.203125 34.421875 6.203125 \nQ 39.59375 6.203125 44.453125 7.46875 \nQ 49.3125 8.734375 54.109375 11.28125 \nL 54.109375 2.78125 \nQ 49.265625 0.734375 44.1875 -0.34375 \nQ 39.109375 -1.421875 33.890625 -1.421875 \nQ 20.796875 -1.421875 13.15625 6.1875 \nQ 5.515625 13.8125 5.515625 26.8125 \nQ 5.515625 40.234375 12.765625 48.109375 \nQ 20.015625 56 32.328125 56 \nQ 43.359375 56 49.78125 48.890625 \nQ 56.203125 41.796875 56.203125 29.59375 \nz\nM 47.21875 32.234375 \nQ 47.125 39.59375 43.09375 43.984375 \nQ 39.0625 48.390625 32.421875 48.390625 \nQ 24.90625 48.390625 20.390625 44.140625 \nQ 15.875 39.890625 15.1875 32.171875 \nz\n\" id=\"DejaVuSans-101\"/>\n       <path d=\"M 41.109375 46.296875 \nQ 39.59375 47.171875 37.8125 47.578125 \nQ 36.03125 48 33.890625 48 \nQ 26.265625 48 22.1875 43.046875 \nQ 18.109375 38.09375 18.109375 28.8125 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 20.953125 51.171875 25.484375 53.578125 \nQ 30.03125 56 36.53125 56 \nQ 37.453125 56 38.578125 55.875 \nQ 39.703125 55.765625 41.0625 55.515625 \nz\n\" id=\"DejaVuSans-114\"/>\n       <path d=\"M 34.28125 27.484375 \nQ 23.390625 27.484375 19.1875 25 \nQ 14.984375 22.515625 14.984375 16.5 \nQ 14.984375 11.71875 18.140625 8.90625 \nQ 21.296875 6.109375 26.703125 6.109375 \nQ 34.1875 6.109375 38.703125 11.40625 \nQ 43.21875 16.703125 43.21875 25.484375 \nL 43.21875 27.484375 \nz\nM 52.203125 31.203125 \nL 52.203125 0 \nL 43.21875 0 \nL 43.21875 8.296875 \nQ 40.140625 3.328125 35.546875 0.953125 \nQ 30.953125 -1.421875 24.3125 -1.421875 \nQ 15.921875 -1.421875 10.953125 3.296875 \nQ 6 8.015625 6 15.921875 \nQ 6 25.140625 12.171875 29.828125 \nQ 18.359375 34.515625 30.609375 34.515625 \nL 43.21875 34.515625 \nL 43.21875 35.40625 \nQ 43.21875 41.609375 39.140625 45 \nQ 35.0625 48.390625 27.6875 48.390625 \nQ 23 48.390625 18.546875 47.265625 \nQ 14.109375 46.140625 10.015625 43.890625 \nL 10.015625 52.203125 \nQ 14.9375 54.109375 19.578125 55.046875 \nQ 24.21875 56 28.609375 56 \nQ 40.484375 56 46.34375 49.84375 \nQ 52.203125 43.703125 52.203125 31.203125 \nz\n\" id=\"DejaVuSans-97\"/>\n       <path d=\"M 9.421875 54.6875 \nL 18.40625 54.6875 \nL 18.40625 0 \nL 9.421875 0 \nz\nM 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 64.59375 \nL 9.421875 64.59375 \nz\n\" id=\"DejaVuSans-105\"/>\n       <path d=\"M 30.609375 48.390625 \nQ 23.390625 48.390625 19.1875 42.75 \nQ 14.984375 37.109375 14.984375 27.296875 \nQ 14.984375 17.484375 19.15625 11.84375 \nQ 23.34375 6.203125 30.609375 6.203125 \nQ 37.796875 6.203125 41.984375 11.859375 \nQ 46.1875 17.53125 46.1875 27.296875 \nQ 46.1875 37.015625 41.984375 42.703125 \nQ 37.796875 48.390625 30.609375 48.390625 \nz\nM 30.609375 56 \nQ 42.328125 56 49.015625 48.375 \nQ 55.71875 40.765625 55.71875 27.296875 \nQ 55.71875 13.875 49.015625 6.21875 \nQ 42.328125 -1.421875 30.609375 -1.421875 \nQ 18.84375 -1.421875 12.171875 6.21875 \nQ 5.515625 13.875 5.515625 27.296875 \nQ 5.515625 40.765625 12.171875 48.375 \nQ 18.84375 56 30.609375 56 \nz\n\" id=\"DejaVuSans-111\"/>\n       <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-110\"/>\n       <path d=\"M 44.28125 53.078125 \nL 44.28125 44.578125 \nQ 40.484375 46.53125 36.375 47.5 \nQ 32.28125 48.484375 27.875 48.484375 \nQ 21.1875 48.484375 17.84375 46.4375 \nQ 14.5 44.390625 14.5 40.28125 \nQ 14.5 37.15625 16.890625 35.375 \nQ 19.28125 33.59375 26.515625 31.984375 \nL 29.59375 31.296875 \nQ 39.15625 29.25 43.1875 25.515625 \nQ 47.21875 21.78125 47.21875 15.09375 \nQ 47.21875 7.46875 41.1875 3.015625 \nQ 35.15625 -1.421875 24.609375 -1.421875 \nQ 20.21875 -1.421875 15.453125 -0.5625 \nQ 10.6875 0.296875 5.421875 2 \nL 5.421875 11.28125 \nQ 10.40625 8.6875 15.234375 7.390625 \nQ 20.0625 6.109375 24.8125 6.109375 \nQ 31.15625 6.109375 34.5625 8.28125 \nQ 37.984375 10.453125 37.984375 14.40625 \nQ 37.984375 18.0625 35.515625 20.015625 \nQ 33.0625 21.96875 24.703125 23.78125 \nL 21.578125 24.515625 \nQ 13.234375 26.265625 9.515625 29.90625 \nQ 5.8125 33.546875 5.8125 39.890625 \nQ 5.8125 47.609375 11.28125 51.796875 \nQ 16.75 56 26.8125 56 \nQ 31.78125 56 36.171875 55.265625 \nQ 40.578125 54.546875 44.28125 53.078125 \nz\n\" id=\"DejaVuSans-115\"/>\n      </defs>\n      <use xlink:href=\"#DejaVuSans-73\"/>\n      <use x=\"29.492188\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"68.701172\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"130.224609\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"171.337891\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"232.617188\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"271.826172\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"299.609375\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"360.791016\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"424.169922\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_2\">\n    <g id=\"ytick_1\">\n     <g id=\"line2d_7\">\n      <defs>\n       <path d=\"M 0 0 \nL -3.5 0 \n\" id=\"mffd16ea1e6\" style=\"stroke:#000000;stroke-width:0.8;\"/>\n      </defs>\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mffd16ea1e6\" y=\"385.541768\"/>\n      </g>\n     </g>\n     <g id=\"text_8\">\n      <!-- 0.00 -->\n      <g transform=\"translate(20.878125 389.340987)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 10.6875 12.40625 \nL 21 12.40625 \nL 21 0 \nL 10.6875 0 \nz\n\" id=\"DejaVuSans-46\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_2\">\n     <g id=\"line2d_8\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mffd16ea1e6\" y=\"301.884905\"/>\n      </g>\n     </g>\n     <g id=\"text_9\">\n      <!-- 0.01 -->\n      <g transform=\"translate(20.878125 305.684124)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_3\">\n     <g id=\"line2d_9\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mffd16ea1e6\" y=\"218.228042\"/>\n      </g>\n     </g>\n     <g id=\"text_10\">\n      <!-- 0.02 -->\n      <g transform=\"translate(20.878125 222.027261)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_4\">\n     <g id=\"line2d_10\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mffd16ea1e6\" y=\"134.57118\"/>\n      </g>\n     </g>\n     <g id=\"text_11\">\n      <!-- 0.03 -->\n      <g transform=\"translate(20.878125 138.370398)scale(0.1 -0.1)\">\n       <defs>\n        <path d=\"M 40.578125 39.3125 \nQ 47.65625 37.796875 51.625 33 \nQ 55.609375 28.21875 55.609375 21.1875 \nQ 55.609375 10.40625 48.1875 4.484375 \nQ 40.765625 -1.421875 27.09375 -1.421875 \nQ 22.515625 -1.421875 17.65625 -0.515625 \nQ 12.796875 0.390625 7.625 2.203125 \nL 7.625 11.71875 \nQ 11.71875 9.328125 16.59375 8.109375 \nQ 21.484375 6.890625 26.8125 6.890625 \nQ 36.078125 6.890625 40.9375 10.546875 \nQ 45.796875 14.203125 45.796875 21.1875 \nQ 45.796875 27.640625 41.28125 31.265625 \nQ 36.765625 34.90625 28.71875 34.90625 \nL 20.21875 34.90625 \nL 20.21875 43.015625 \nL 29.109375 43.015625 \nQ 36.375 43.015625 40.234375 45.921875 \nQ 44.09375 48.828125 44.09375 54.296875 \nQ 44.09375 59.90625 40.109375 62.90625 \nQ 36.140625 65.921875 28.71875 65.921875 \nQ 24.65625 65.921875 20.015625 65.03125 \nQ 15.375 64.15625 9.8125 62.3125 \nL 9.8125 71.09375 \nQ 15.4375 72.65625 20.34375 73.4375 \nQ 25.25 74.21875 29.59375 74.21875 \nQ 40.828125 74.21875 47.359375 69.109375 \nQ 53.90625 64.015625 53.90625 55.328125 \nQ 53.90625 49.265625 50.4375 45.09375 \nQ 46.96875 40.921875 40.578125 39.3125 \nz\n\" id=\"DejaVuSans-51\"/>\n       </defs>\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-51\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_5\">\n     <g id=\"line2d_11\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"50.14375\" xlink:href=\"#mffd16ea1e6\" y=\"50.914317\"/>\n      </g>\n     </g>\n     <g id=\"text_12\">\n      <!-- 0.04 -->\n      <g transform=\"translate(20.878125 54.713535)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_13\">\n     <!-- Cost -->\n     <g transform=\"translate(14.798437 223.69375)rotate(-90)scale(0.1 -0.1)\">\n      <defs>\n       <path d=\"M 64.40625 67.28125 \nL 64.40625 56.890625 \nQ 59.421875 61.53125 53.78125 63.8125 \nQ 48.140625 66.109375 41.796875 66.109375 \nQ 29.296875 66.109375 22.65625 58.46875 \nQ 16.015625 50.828125 16.015625 36.375 \nQ 16.015625 21.96875 22.65625 14.328125 \nQ 29.296875 6.6875 41.796875 6.6875 \nQ 48.140625 6.6875 53.78125 8.984375 \nQ 59.421875 11.28125 64.40625 15.921875 \nL 64.40625 5.609375 \nQ 59.234375 2.09375 53.4375 0.328125 \nQ 47.65625 -1.421875 41.21875 -1.421875 \nQ 24.65625 -1.421875 15.125 8.703125 \nQ 5.609375 18.84375 5.609375 36.375 \nQ 5.609375 53.953125 15.125 64.078125 \nQ 24.65625 74.21875 41.21875 74.21875 \nQ 47.75 74.21875 53.53125 72.484375 \nQ 59.328125 70.75 64.40625 67.28125 \nz\n\" id=\"DejaVuSans-67\"/>\n      </defs>\n      <use xlink:href=\"#DejaVuSans-67\"/>\n      <use x=\"69.824219\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"131.005859\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"183.105469\" xlink:href=\"#DejaVuSans-116\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_12\">\n    <path clip-path=\"url(#p92c7321722)\" d=\"M 67.437138 39.614489 \nL 70.930752 39.614489 \nL 74.424366 39.614489 \nL 77.91798 39.614489 \nL 81.411594 39.614489 \nL 84.905208 373.235164 \nL 88.398821 373.235164 \nL 91.892435 373.235164 \nL 95.386049 373.235164 \nL 98.879663 373.235164 \nL 102.373277 373.235164 \nL 105.86689 377.823505 \nL 109.360504 377.823505 \nL 112.854118 377.823505 \nL 116.347732 377.823505 \nL 119.841346 377.823505 \nL 123.33496 377.823505 \nL 126.828573 377.823505 \nL 130.322187 377.823505 \nL 133.815801 377.823505 \nL 137.309415 383.630245 \nL 140.803029 383.630245 \nL 144.296643 383.630245 \nL 147.790256 383.630245 \nL 151.28387 383.630245 \nL 154.777484 385.429244 \nL 158.271098 385.429244 \nL 161.764712 385.429244 \nL 165.258326 385.429244 \nL 168.751939 385.429244 \nL 172.245553 385.429244 \nL 175.739167 385.429244 \nL 179.232781 385.429244 \nL 182.726395 385.429244 \nL 186.220008 385.429244 \nL 189.713622 385.429244 \nL 193.207236 385.429244 \nL 196.70085 385.429244 \nL 200.194464 385.429244 \nL 203.688078 385.429244 \nL 207.181691 385.429244 \nL 210.675305 385.429244 \nL 214.168919 385.460179 \nL 217.662533 385.523163 \nL 221.156147 385.523163 \nL 224.649761 385.523163 \nL 228.143374 385.523163 \nL 231.636988 385.523163 \nL 235.130602 385.523163 \nL 238.624216 385.523163 \nL 242.11783 385.523163 \nL 245.611443 385.523163 \nL 249.105057 385.530132 \nL 252.598671 385.530132 \nL 256.092285 385.530132 \nL 259.585899 385.530132 \nL 263.079513 385.530132 \nL 266.573126 385.530132 \nL 270.06674 385.530132 \nL 273.560354 385.530132 \nL 277.053968 385.536214 \nL 280.547582 385.536214 \nL 284.041196 385.536214 \nL 287.534809 385.539715 \nL 291.028423 385.539715 \nL 294.522037 385.539715 \nL 298.015651 385.539715 \nL 301.509265 385.539715 \nL 305.002878 385.539715 \nL 308.496492 385.539715 \nL 311.990106 385.539715 \nL 315.48372 385.540063 \nL 318.977334 385.540063 \nL 322.470948 385.54143 \nL 325.964561 385.54143 \nL 329.458175 385.54143 \nL 332.951789 385.54143 \nL 336.445403 385.54143 \nL 339.939017 385.54143 \nL 343.432631 385.54143 \nL 346.926244 385.54143 \nL 350.419858 385.54143 \nL 353.913472 385.54143 \nL 357.407086 385.54143 \nL 360.9007 385.54143 \nL 364.394313 385.54143 \nL 367.887927 385.54143 \nL 371.381541 385.54143 \nL 374.875155 385.541513 \nL 378.368769 385.541513 \nL 381.862383 385.541513 \nL 385.355996 385.541513 \nL 388.84961 385.541513 \nL 392.343224 385.541513 \nL 395.836838 385.541513 \nL 399.330452 385.541513 \nL 402.824066 385.541513 \nL 406.317679 385.541513 \nL 409.811293 385.541513 \nL 413.304907 385.541761 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-width:2;\"/>\n   </g>\n   <g id=\"patch_3\">\n    <path d=\"M 50.14375 402.838125 \nL 50.14375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_4\">\n    <path d=\"M 430.**********.838125 \nL 430.598295 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_5\">\n    <path d=\"M 50.14375 402.838125 \nL 430.**********.838125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_6\">\n    <path d=\"M 50.14375 22.318125 \nL 430.598295 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"text_14\">\n    <!-- Cost history without inertia decay -->\n    <g transform=\"translate(138.862273 16.318125)scale(0.12 -0.12)\">\n     <defs>\n      <path id=\"DejaVuSans-32\"/>\n      <path d=\"M 54.890625 33.015625 \nL 54.890625 0 \nL 45.90625 0 \nL 45.90625 32.71875 \nQ 45.90625 40.484375 42.875 44.328125 \nQ 39.84375 48.1875 33.796875 48.1875 \nQ 26.515625 48.1875 22.3125 43.546875 \nQ 18.109375 38.921875 18.109375 30.90625 \nL 18.109375 0 \nL 9.078125 0 \nL 9.078125 75.984375 \nL 18.109375 75.984375 \nL 18.109375 46.1875 \nQ 21.34375 51.125 25.703125 53.5625 \nQ 30.078125 56 35.796875 56 \nQ 45.21875 56 50.046875 50.171875 \nQ 54.890625 44.34375 54.890625 33.015625 \nz\n\" id=\"DejaVuSans-104\"/>\n      <path d=\"M 32.171875 -5.078125 \nQ 28.375 -14.84375 24.75 -17.8125 \nQ 21.140625 -20.796875 15.09375 -20.796875 \nL 7.90625 -20.796875 \nL 7.90625 -13.28125 \nL 13.1875 -13.28125 \nQ 16.890625 -13.28125 18.9375 -11.515625 \nQ 21 -9.765625 23.484375 -3.21875 \nL 25.09375 0.875 \nL 2.984375 54.6875 \nL 12.5 54.6875 \nL 29.59375 11.921875 \nL 46.6875 54.6875 \nL 56.203125 54.6875 \nz\n\" id=\"DejaVuSans-121\"/>\n      <path d=\"M 4.203125 54.6875 \nL 13.1875 54.6875 \nL 24.421875 12.015625 \nL 35.59375 54.6875 \nL 46.1875 54.6875 \nL 57.421875 12.015625 \nL 68.609375 54.6875 \nL 77.59375 54.6875 \nL 63.28125 0 \nL 52.6875 0 \nL 40.921875 44.828125 \nL 29.109375 0 \nL 18.5 0 \nz\n\" id=\"DejaVuSans-119\"/>\n      <path d=\"M 8.5 21.578125 \nL 8.5 54.6875 \nL 17.484375 54.6875 \nL 17.484375 21.921875 \nQ 17.484375 14.15625 20.5 10.265625 \nQ 23.53125 6.390625 29.59375 6.390625 \nQ 36.859375 6.390625 41.078125 11.03125 \nQ 45.3125 15.671875 45.3125 23.6875 \nL 45.3125 54.6875 \nL 54.296875 54.6875 \nL 54.296875 0 \nL 45.3125 0 \nL 45.3125 8.40625 \nQ 42.046875 3.421875 37.71875 1 \nQ 33.40625 -1.421875 27.6875 -1.421875 \nQ 18.265625 -1.421875 13.375 4.4375 \nQ 8.5 10.296875 8.5 21.578125 \nz\nM 31.109375 56 \nz\n\" id=\"DejaVuSans-117\"/>\n      <path d=\"M 45.40625 46.390625 \nL 45.40625 75.984375 \nL 54.390625 75.984375 \nL 54.390625 0 \nL 45.40625 0 \nL 45.40625 8.203125 \nQ 42.578125 3.328125 38.25 0.953125 \nQ 33.9375 -1.421875 27.875 -1.421875 \nQ 17.96875 -1.421875 11.734375 6.484375 \nQ 5.515625 14.40625 5.515625 27.296875 \nQ 5.515625 40.1875 11.734375 48.09375 \nQ 17.96875 56 27.875 56 \nQ 33.9375 56 38.25 53.625 \nQ 42.578125 51.265625 45.40625 46.390625 \nz\nM 14.796875 27.296875 \nQ 14.796875 17.390625 18.875 11.75 \nQ 22.953125 6.109375 30.078125 6.109375 \nQ 37.203125 6.109375 41.296875 11.75 \nQ 45.40625 17.390625 45.40625 27.296875 \nQ 45.40625 37.203125 41.296875 42.84375 \nQ 37.203125 48.484375 30.078125 48.484375 \nQ 22.953125 48.484375 18.875 42.84375 \nQ 14.796875 37.203125 14.796875 27.296875 \nz\n\" id=\"DejaVuSans-100\"/>\n      <path d=\"M 48.78125 52.59375 \nL 48.78125 44.1875 \nQ 44.96875 46.296875 41.140625 47.34375 \nQ 37.3125 48.390625 33.40625 48.390625 \nQ 24.65625 48.390625 19.8125 42.84375 \nQ 14.984375 37.3125 14.984375 27.296875 \nQ 14.984375 17.28125 19.8125 11.734375 \nQ 24.65625 6.203125 33.40625 6.203125 \nQ 37.3125 6.203125 41.140625 7.25 \nQ 44.96875 8.296875 48.78125 10.40625 \nL 48.78125 2.09375 \nQ 45.015625 0.34375 40.984375 -0.53125 \nQ 36.96875 -1.421875 32.421875 -1.421875 \nQ 20.0625 -1.421875 12.78125 6.34375 \nQ 5.515625 14.109375 5.515625 27.296875 \nQ 5.515625 40.671875 12.859375 48.328125 \nQ 20.21875 56 33.015625 56 \nQ 37.15625 56 41.109375 55.140625 \nQ 45.0625 54.296875 48.78125 52.59375 \nz\n\" id=\"DejaVuSans-99\"/>\n     </defs>\n     <use xlink:href=\"#DejaVuSans-67\"/>\n     <use x=\"69.824219\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"131.005859\" xlink:href=\"#DejaVuSans-115\"/>\n     <use x=\"183.105469\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"222.314453\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"254.101562\" xlink:href=\"#DejaVuSans-104\"/>\n     <use x=\"317.480469\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"345.263672\" xlink:href=\"#DejaVuSans-115\"/>\n     <use x=\"397.363281\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"436.572266\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"497.753906\" xlink:href=\"#DejaVuSans-114\"/>\n     <use x=\"538.867188\" xlink:href=\"#DejaVuSans-121\"/>\n     <use x=\"598.046875\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"629.833984\" xlink:href=\"#DejaVuSans-119\"/>\n     <use x=\"711.621094\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"739.404297\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"778.613281\" xlink:href=\"#DejaVuSans-104\"/>\n     <use x=\"841.992188\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"903.173828\" xlink:href=\"#DejaVuSans-117\"/>\n     <use x=\"966.552734\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"1005.761719\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"1037.548828\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1065.332031\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"1128.710938\" xlink:href=\"#DejaVuSans-101\"/>\n     <use x=\"1190.234375\" xlink:href=\"#DejaVuSans-114\"/>\n     <use x=\"1231.347656\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"1270.556641\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1298.339844\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"1359.619141\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"1391.40625\" xlink:href=\"#DejaVuSans-100\"/>\n     <use x=\"1454.882812\" xlink:href=\"#DejaVuSans-101\"/>\n     <use x=\"1516.40625\" xlink:href=\"#DejaVuSans-99\"/>\n     <use x=\"1571.386719\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"1632.666016\" xlink:href=\"#DejaVuSans-121\"/>\n    </g>\n   </g>\n   <g id=\"legend_1\">\n    <g id=\"patch_7\">\n     <path d=\"M 369.367045 44.99625 \nL 423.598295 44.99625 \nQ 425.598295 44.99625 425.598295 42.99625 \nL 425.598295 29.318125 \nQ 425.598295 27.318125 423.598295 27.318125 \nL 369.367045 27.318125 \nQ 367.367045 27.318125 367.367045 29.318125 \nL 367.367045 42.99625 \nQ 367.367045 44.99625 369.367045 44.99625 \nz\n\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n    </g>\n    <g id=\"line2d_13\">\n     <path d=\"M 371.367045 35.416562 \nL 391.367045 35.416562 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-width:2;\"/>\n    </g>\n    <g id=\"line2d_14\"/>\n    <g id=\"text_15\">\n     <!-- Cost -->\n     <g transform=\"translate(399.367045 38.916562)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-67\"/>\n      <use x=\"69.824219\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"131.005859\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"183.105469\" xlink:href=\"#DejaVuSans-116\"/>\n     </g>\n    </g>\n   </g>\n  </g>\n  <g id=\"axes_2\">\n   <g id=\"patch_8\">\n    <path d=\"M 506.689205 402.838125 \nL 887.14375 402.838125 \nL 887.14375 22.318125 \nL 506.689205 22.318125 \nz\n\" style=\"fill:#ffffff;\"/>\n   </g>\n   <g id=\"matplotlib.axis_3\">\n    <g id=\"xtick_7\">\n     <g id=\"line2d_15\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"523.982593\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_16\">\n      <!-- 0 -->\n      <g transform=\"translate(520.801343 417.436562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_8\">\n     <g id=\"line2d_16\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"593.854869\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_17\">\n      <!-- 20 -->\n      <g transform=\"translate(587.492369 417.436562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-50\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_9\">\n     <g id=\"line2d_17\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"663.727146\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_18\">\n      <!-- 40 -->\n      <g transform=\"translate(657.364646 417.436562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-52\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_10\">\n     <g id=\"line2d_18\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"733.599422\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_19\">\n      <!-- 60 -->\n      <g transform=\"translate(727.236922 417.436562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-54\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_11\">\n     <g id=\"line2d_19\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"803.471699\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_20\">\n      <!-- 80 -->\n      <g transform=\"translate(797.109199 417.436562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-56\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"xtick_12\">\n     <g id=\"line2d_20\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"873.343975\" xlink:href=\"#m4c577bf323\" y=\"402.838125\"/>\n      </g>\n     </g>\n     <g id=\"text_21\">\n      <!-- 100 -->\n      <g transform=\"translate(863.800225 417.436562)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"127.246094\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_22\">\n     <!-- Iterations -->\n     <g transform=\"translate(673.103196 431.114687)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-73\"/>\n      <use x=\"29.492188\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"68.701172\" xlink:href=\"#DejaVuSans-101\"/>\n      <use x=\"130.224609\" xlink:href=\"#DejaVuSans-114\"/>\n      <use x=\"171.337891\" xlink:href=\"#DejaVuSans-97\"/>\n      <use x=\"232.617188\" xlink:href=\"#DejaVuSans-116\"/>\n      <use x=\"271.826172\" xlink:href=\"#DejaVuSans-105\"/>\n      <use x=\"299.609375\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"360.791016\" xlink:href=\"#DejaVuSans-110\"/>\n      <use x=\"424.169922\" xlink:href=\"#DejaVuSans-115\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"matplotlib.axis_4\">\n    <g id=\"ytick_6\">\n     <g id=\"line2d_21\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"385.541761\"/>\n      </g>\n     </g>\n     <g id=\"text_23\">\n      <!-- 0.000 -->\n      <g transform=\"translate(471.06108 389.34098)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_7\">\n     <g id=\"line2d_22\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"333.055017\"/>\n      </g>\n     </g>\n     <g id=\"text_24\">\n      <!-- 0.002 -->\n      <g transform=\"translate(471.06108 336.854236)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_8\">\n     <g id=\"line2d_23\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"280.568273\"/>\n      </g>\n     </g>\n     <g id=\"text_25\">\n      <!-- 0.004 -->\n      <g transform=\"translate(471.06108 284.367492)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-52\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_9\">\n     <g id=\"line2d_24\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"228.081529\"/>\n      </g>\n     </g>\n     <g id=\"text_26\">\n      <!-- 0.006 -->\n      <g transform=\"translate(471.06108 231.880748)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-54\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_10\">\n     <g id=\"line2d_25\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"175.594785\"/>\n      </g>\n     </g>\n     <g id=\"text_27\">\n      <!-- 0.008 -->\n      <g transform=\"translate(471.06108 179.394004)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-56\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_11\">\n     <g id=\"line2d_26\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"123.108041\"/>\n      </g>\n     </g>\n     <g id=\"text_28\">\n      <!-- 0.010 -->\n      <g transform=\"translate(471.06108 126.90726)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-48\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"ytick_12\">\n     <g id=\"line2d_27\">\n      <g>\n       <use style=\"stroke:#000000;stroke-width:0.8;\" x=\"506.689205\" xlink:href=\"#mffd16ea1e6\" y=\"70.621297\"/>\n      </g>\n     </g>\n     <g id=\"text_29\">\n      <!-- 0.012 -->\n      <g transform=\"translate(471.06108 74.420516)scale(0.1 -0.1)\">\n       <use xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"63.623047\" xlink:href=\"#DejaVuSans-46\"/>\n       <use x=\"95.410156\" xlink:href=\"#DejaVuSans-48\"/>\n       <use x=\"159.033203\" xlink:href=\"#DejaVuSans-49\"/>\n       <use x=\"222.65625\" xlink:href=\"#DejaVuSans-50\"/>\n      </g>\n     </g>\n    </g>\n    <g id=\"text_30\">\n     <!-- Cost -->\n     <g transform=\"translate(464.981392 223.69375)rotate(-90)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-67\"/>\n      <use x=\"69.824219\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"131.005859\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"183.105469\" xlink:href=\"#DejaVuSans-116\"/>\n     </g>\n    </g>\n   </g>\n   <g id=\"line2d_28\">\n    <path clip-path=\"url(#pae8e51acef)\" d=\"M 523.982593 39.614489 \nL 527.476207 39.614489 \nL 530.969821 39.614489 \nL 534.463434 39.614489 \nL 537.957048 39.614489 \nL 541.450662 277.951453 \nL 544.944276 384.185268 \nL 548.43789 384.185268 \nL 551.931504 384.185268 \nL 555.425117 384.185268 \nL 558.918731 384.692216 \nL 562.412345 384.692216 \nL 565.905959 384.692216 \nL 569.399573 385.303826 \nL 572.893187 385.303826 \nL 576.3868 385.359759 \nL 579.880414 385.359759 \nL 583.374028 385.540006 \nL 586.867642 385.540301 \nL 590.361256 385.540301 \nL 593.854869 385.540301 \nL 597.348483 385.54071 \nL 600.842097 385.541547 \nL 604.335711 385.541547 \nL 607.829325 385.541547 \nL 611.322939 385.541681 \nL 614.816552 385.541726 \nL 618.310166 385.541743 \nL 621.80378 385.541751 \nL 625.297394 385.541755 \nL 628.791008 385.541761 \nL 632.284622 385.541761 \nL 635.778235 385.541761 \nL 639.271849 385.541761 \nL 642.765463 385.541761 \nL 646.259077 385.541761 \nL 649.752691 385.541761 \nL 653.246304 385.541761 \nL 656.739918 385.541761 \nL 660.233532 385.541761 \nL 663.727146 385.541761 \nL 667.22076 385.541761 \nL 670.714374 385.541761 \nL 674.207987 385.541761 \nL 677.701601 385.541761 \nL 681.195215 385.541761 \nL 684.688829 385.541761 \nL 688.182443 385.541761 \nL 691.676057 385.541761 \nL 695.16967 385.541761 \nL 698.663284 385.541761 \nL 702.156898 385.541761 \nL 705.650512 385.541761 \nL 709.144126 385.541761 \nL 712.637739 385.541761 \nL 716.131353 385.541761 \nL 719.624967 385.541761 \nL 723.118581 385.541761 \nL 726.612195 385.541761 \nL 730.105809 385.541761 \nL 733.599422 385.541761 \nL 737.093036 385.541761 \nL 740.58665 385.541761 \nL 744.080264 385.541761 \nL 747.573878 385.541761 \nL 751.067492 385.541761 \nL 754.561105 385.541761 \nL 758.054719 385.541761 \nL 761.548333 385.541761 \nL 765.041947 385.541761 \nL 768.535561 385.541761 \nL 772.029174 385.541761 \nL 775.522788 385.541761 \nL 779.016402 385.541761 \nL 782.510016 385.541761 \nL 786.00363 385.541761 \nL 789.497244 385.541761 \nL 792.990857 385.541761 \nL 796.484471 385.541761 \nL 799.978085 385.541761 \nL 803.471699 385.541761 \nL 806.965313 385.541761 \nL 810.458927 385.541761 \nL 813.95254 385.541761 \nL 817.446154 385.541761 \nL 820.939768 385.541761 \nL 824.433382 385.541761 \nL 827.926996 385.541761 \nL 831.42061 385.541761 \nL 834.914223 385.541761 \nL 838.407837 385.541761 \nL 841.901451 385.541761 \nL 845.395065 385.541761 \nL 848.888679 385.541761 \nL 852.382292 385.541761 \nL 855.875906 385.541761 \nL 859.36952 385.541761 \nL 862.863134 385.541761 \nL 866.356748 385.541761 \nL 869.850362 385.541761 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-width:2;\"/>\n   </g>\n   <g id=\"patch_9\">\n    <path d=\"M 506.689205 402.838125 \nL 506.689205 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_10\">\n    <path d=\"M 887.14375 402.838125 \nL 887.14375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_11\">\n    <path d=\"M 506.689205 402.838125 \nL 887.14375 402.838125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"patch_12\">\n    <path d=\"M 506.689205 22.318125 \nL 887.14375 22.318125 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;stroke-width:0.8;\"/>\n   </g>\n   <g id=\"text_31\">\n    <!-- Cost history with exponential intertia decay -->\n    <g transform=\"translate(565.696477 16.318125)scale(0.12 -0.12)\">\n     <defs>\n      <path d=\"M 54.890625 54.6875 \nL 35.109375 28.078125 \nL 55.90625 0 \nL 45.3125 0 \nL 29.390625 21.484375 \nL 13.484375 0 \nL 2.875 0 \nL 24.125 28.609375 \nL 4.6875 54.6875 \nL 15.28125 54.6875 \nL 29.78125 35.203125 \nL 44.28125 54.6875 \nz\n\" id=\"DejaVuSans-120\"/>\n      <path d=\"M 18.109375 8.203125 \nL 18.109375 -20.796875 \nL 9.078125 -20.796875 \nL 9.078125 54.6875 \nL 18.109375 54.6875 \nL 18.109375 46.390625 \nQ 20.953125 51.265625 25.265625 53.625 \nQ 29.59375 56 35.59375 56 \nQ 45.5625 56 51.78125 48.09375 \nQ 58.015625 40.1875 58.015625 27.296875 \nQ 58.015625 14.40625 51.78125 6.484375 \nQ 45.5625 -1.421875 35.59375 -1.421875 \nQ 29.59375 -1.421875 25.265625 0.953125 \nQ 20.953125 3.328125 18.109375 8.203125 \nz\nM 48.6875 27.296875 \nQ 48.6875 37.203125 44.609375 42.84375 \nQ 40.53125 48.484375 33.40625 48.484375 \nQ 26.265625 48.484375 22.1875 42.84375 \nQ 18.109375 37.203125 18.109375 27.296875 \nQ 18.109375 17.390625 22.1875 11.75 \nQ 26.265625 6.109375 33.40625 6.109375 \nQ 40.53125 6.109375 44.609375 11.75 \nQ 48.6875 17.390625 48.6875 27.296875 \nz\n\" id=\"DejaVuSans-112\"/>\n      <path d=\"M 9.421875 75.984375 \nL 18.40625 75.984375 \nL 18.40625 0 \nL 9.421875 0 \nz\n\" id=\"DejaVuSans-108\"/>\n     </defs>\n     <use xlink:href=\"#DejaVuSans-67\"/>\n     <use x=\"69.824219\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"131.005859\" xlink:href=\"#DejaVuSans-115\"/>\n     <use x=\"183.105469\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"222.314453\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"254.101562\" xlink:href=\"#DejaVuSans-104\"/>\n     <use x=\"317.480469\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"345.263672\" xlink:href=\"#DejaVuSans-115\"/>\n     <use x=\"397.363281\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"436.572266\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"497.753906\" xlink:href=\"#DejaVuSans-114\"/>\n     <use x=\"538.867188\" xlink:href=\"#DejaVuSans-121\"/>\n     <use x=\"598.046875\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"629.833984\" xlink:href=\"#DejaVuSans-119\"/>\n     <use x=\"711.621094\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"739.404297\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"778.613281\" xlink:href=\"#DejaVuSans-104\"/>\n     <use x=\"841.992188\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"873.779297\" xlink:href=\"#DejaVuSans-101\"/>\n     <use x=\"933.552734\" xlink:href=\"#DejaVuSans-120\"/>\n     <use x=\"992.732422\" xlink:href=\"#DejaVuSans-112\"/>\n     <use x=\"1056.208984\" xlink:href=\"#DejaVuSans-111\"/>\n     <use x=\"1117.390625\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"1180.769531\" xlink:href=\"#DejaVuSans-101\"/>\n     <use x=\"1242.292969\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"1305.671875\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"1344.880859\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1372.664062\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"1433.943359\" xlink:href=\"#DejaVuSans-108\"/>\n     <use x=\"1461.726562\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"1493.513672\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1521.296875\" xlink:href=\"#DejaVuSans-110\"/>\n     <use x=\"1584.675781\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"1623.884766\" xlink:href=\"#DejaVuSans-101\"/>\n     <use x=\"1685.408203\" xlink:href=\"#DejaVuSans-114\"/>\n     <use x=\"1726.521484\" xlink:href=\"#DejaVuSans-116\"/>\n     <use x=\"1765.730469\" xlink:href=\"#DejaVuSans-105\"/>\n     <use x=\"1793.513672\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"1854.792969\" xlink:href=\"#DejaVuSans-32\"/>\n     <use x=\"1886.580078\" xlink:href=\"#DejaVuSans-100\"/>\n     <use x=\"1950.056641\" xlink:href=\"#DejaVuSans-101\"/>\n     <use x=\"2011.580078\" xlink:href=\"#DejaVuSans-99\"/>\n     <use x=\"2066.560547\" xlink:href=\"#DejaVuSans-97\"/>\n     <use x=\"2127.839844\" xlink:href=\"#DejaVuSans-121\"/>\n    </g>\n   </g>\n   <g id=\"legend_2\">\n    <g id=\"patch_13\">\n     <path d=\"M 825.9125 44.99625 \nL 880.14375 44.99625 \nQ 882.14375 44.99625 882.14375 42.99625 \nL 882.14375 29.318125 \nQ 882.14375 27.318125 880.14375 27.318125 \nL 825.9125 27.318125 \nQ 823.9125 27.318125 823.9125 29.318125 \nL 823.9125 42.99625 \nQ 823.9125 44.99625 825.9125 44.99625 \nz\n\" style=\"fill:#ffffff;opacity:0.8;stroke:#cccccc;stroke-linejoin:miter;\"/>\n    </g>\n    <g id=\"line2d_29\">\n     <path d=\"M 827.9125 35.416562 \nL 847.9125 35.416562 \n\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-width:2;\"/>\n    </g>\n    <g id=\"line2d_30\"/>\n    <g id=\"text_32\">\n     <!-- Cost -->\n     <g transform=\"translate(855.9125 38.916562)scale(0.1 -0.1)\">\n      <use xlink:href=\"#DejaVuSans-67\"/>\n      <use x=\"69.824219\" xlink:href=\"#DejaVuSans-111\"/>\n      <use x=\"131.005859\" xlink:href=\"#DejaVuSans-115\"/>\n      <use x=\"183.105469\" xlink:href=\"#DejaVuSans-116\"/>\n     </g>\n    </g>\n   </g>\n  </g>\n </g>\n <defs>\n  <clipPath id=\"p92c7321722\">\n   <rect height=\"380.52\" width=\"380.454545\" x=\"50.14375\" y=\"22.318125\"/>\n  </clipPath>\n  <clipPath id=\"pae8e51acef\">\n   <rect height=\"380.52\" width=\"380.454545\" x=\"506.689205\" y=\"22.318125\"/>\n  </clipPath>\n </defs>\n</svg>\n", "image/png": "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\n"}, "metadata": {"needs_background": "light"}}], "source": ["fig, (ax, ax_h) = plt.subplots(ncols=2, nrows=1)\n", "fig.set_size_inches(15,7)\n", "plot_cost_history(ax=ax, cost_history=optimizer_without_handle.cost_history)\n", "plot_cost_history(ax=ax_h, cost_history=optimizer_with_handle.cost_history)\n", "ax.set_title(\"Cost history without inertia decay\")\n", "ax_h.set_title(\"Cost history with exponential intertia decay\")\n", "plt.show()"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"source": ["The rapid decay of the inertia weight contributes significantly to the overall best cost and position. It also converges a lot faster.\n", "\n", "The next part shows the explanation for this with an animation"], "cell_type": "markdown", "metadata": {}}, {"cell_type": "markdown", "source": ["## Comparing animations\n", "The `plotters` module offers two methods to perform animation, `plot_contour()`. This method plot the particles in a 2-D space.\n", "\n", "The objective function contours are added using the `Mesher` class.\n"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 4, "outputs": [], "source": ["from pyswarms.utils.plotters.formatters import Mesher\n", "# Initialize mesher with sphere function\n", "m = Mesher(func=fx.sphere)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "markdown", "source": ["### Plotting in 2-D space\n", "\n", "We can obtain the swarm's position history using the `pos_history` attribute from the `optimizer` instance. To plot a 2D-contour, simply pass this together with the `Mesher` to the `plot_contour()` function. In addition, we can also mark the global minima of the sphere function, `(0,0)`, to visualize the swarm's \"target\"."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["2020-12-11 19:23:22,596 - matplotlib.animation - WARNING - MovieWriter imagemagick unavailable; using <PERSON><PERSON> instead.\n", "2020-12-11 19:23:22,597 - matplotlib.animation - INFO - Animation.save using <class 'matplotlib.animation.PillowWriter'>\n", "2020-12-11 19:23:30,971 - matplotlib.animation - WARNING - MovieWriter imagemagick unavailable; using <PERSON><PERSON> instead.\n", "2020-12-11 19:23:30,973 - matplotlib.animation - INFO - Animation.save using <class 'matplotlib.animation.PillowWriter'>\n"]}], "source": ["%%capture\n", "\n", "# Make and save animation\n", "animation = plot_contour(pos_history=optimizer_without_handle.pos_history,\n", "                         mesher=m,\n", "                         mark=(0,0))\n", "animation_h = plot_contour(pos_history=optimizer_with_handle.pos_history,\n", "                         mesher=m,\n", "                         mark=(0,0))\n", "# Enables us to view it in a <PERSON><PERSON><PERSON> notebook\n", "animation.save('ani.gif', writer='imagemagick', fps=10)\n", "\n", "animation_h.save('ani_h.gif', writer='imagemagick', fps=10)\n", "\n"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"source": ["Compare the two animations. Observe the convergence time and particle overshoot in both figures.\n", "\n", "Left: Without handle\n", "\n", "Right: With handle\n", "\n", "<img src=\"ani.gif\" width=\"450\" align=\"left\">\n", "<img src=\"ani_h.gif\" width=\"450\" align=\"right\">\n", "\n"], "cell_type": "markdown", "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "source": ["## Customizing ending options\n", "\n", "As of the current version(1.2.0), you'll need to create your own optimization loop to keep custom ending options.\n", "The next block shows a basic implementation of this without logging etc."], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 6, "outputs": [], "source": ["from pyswarms.backend.operators import compute_pbest, compute_objective_function"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"source": ["def optimize(objective_func, maxiters, oh_strategy,start_opts, end_opts):\n", "    opt = ps.single.GlobalBestPSO(n_particles=50, dimensions=2, options=start_opts, oh_strategy=oh_strategy)\n", "\n", "    swarm = opt.swarm\n", "    opt.bh.memory = swarm.position\n", "    opt.vh.memory = swarm.position\n", "    swarm.pbest_cost = np.full(opt.swarm_size[0], np.\n", "    inf)\n", "\n", "    for i in range(maxiters):\n", "        # Compute cost for current position and personal best\n", "        swarm.current_cost =  compute_objective_function(swarm, objective_func)\n", "        swarm.pbest_pos, swarm.pbest_cost = compute_pbest(swarm)\n", "\n", "        # Set best_cost_yet_found for ftol\n", "        best_cost_yet_found = swarm.best_cost\n", "        swarm.best_pos, swarm.best_cost = opt.top.compute_gbest(swarm)\n", "        # Perform options update\n", "        swarm.options = opt.oh( opt.options, iternow=i, itermax=maxiters, end_opts=end_opts )\n", "        print(\"Iteration:\", i,\" Options: \", swarm.options)    # print to see variation\n", "        # Perform velocity and position updates\n", "        swarm.velocity = opt.top.compute_velocity(\n", "            swarm, opt.velocity_clamp, opt.vh, opt.bounds\n", "        )\n", "        swarm.position = opt.top.compute_position(\n", "            swarm, opt.bounds, opt.bh\n", "        )\n", "    # Obtain the final best_cost and the final best_position\n", "    final_best_cost = swarm.best_cost.copy()\n", "    final_best_pos = swarm.pbest_pos[\n", "        swarm.pbest_cost.argmin()\n", "    ].copy()\n", "    return final_best_cost, final_best_pos"], "cell_type": "code", "metadata": {}, "execution_count": 10, "outputs": []}, {"source": ["In the next cell, you can play around with the start and end options, maximum iterations and the function itself to compare the outputs like above."], "cell_type": "markdown", "metadata": {}}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Iteration: 0  Options:  {'c1': 2.5, 'c2': 0.5, 'w': 0.8154845485377135}\nIteration: 1  Options:  {'c1': 2.476024064289623, 'c2': 0.52, 'w': 0.7638427274927154}\nIteration: 2  Options:  {'c1': 2.4520965166602724, 'c2': 0.54, 'w': 0.7212425273766464}\nIteration: 3  Options:  {'c1': 2.428217751727513, 'c2': 0.56, 'w': 0.6855550181382926}\nIteration: 4  Options:  {'c1': 2.4043881714225273, 'c2': 0.5800000000000001, 'w': 0.6552602432446853}\nIteration: 5  Options:  {'c1': 2.3806081852052783, 'c2': 0.6000000000000001, 'w': 0.6292465903435571}\nIteration: 6  Options:  {'c1': 2.3568782102861965, 'c2': 0.6200000000000001, 'w': 0.6066838570323415}\nIteration: 7  Options:  {'c1': 2.3331986718568167, 'c2': 0.6399999999999999, 'w': 0.5869404779457066}\nIteration: 8  Options:  {'c1': 2.3095700033298305, 'c2': 0.6599999999999999, 'w': 0.5695280957195421}\nIteration: 9  Options:  {'c1': 2.285992646589043, 'c2': 0.6799999999999999, 'w': 0.5540635587607113}\nIteration: 10  Options:  {'c1': 2.2624670522497583, 'c2': 0.7, 'w': 0.5402423141269194}\nIteration: 11  Options:  {'c1': 2.2389936799301475, 'c2': 0.72, 'w': 0.527819424452714}\nIteration: 12  Options:  {'c1': 2.215572998534194, 'c2': 0.74, 'w': 0.5165957922398924}\nIteration: 13  Options:  {'c1': 2.192205486546836, 'c2': 0.76, 'w': 0.5064080078932764}\nIteration: 14  Options:  {'c1': 2.1688916323420004, 'c2': 0.78, 'w': 0.4971207626134709}\nIteration: 15  Options:  {'c1': 2.1456319345042183, 'c2': 0.8, 'w': 0.4886211049862233}\nIteration: 16  Options:  {'c1': 2.1224269021646167, 'c2': 0.8200000000000001, 'w': 0.48081404179727616}\nIteration: 17  Options:  {'c1': 2.0992770553520845, 'c2': 0.8400000000000001, 'w': 0.4736191317674961}\nIteration: 18  Options:  {'c1': 2.076182925360504, 'c2': 0.8600000000000001, 'w': 0.46696782158333405}\nIteration: 19  Options:  {'c1': 2.053145055132976, 'c2': 0.8799999999999999, 'w': 0.4608013430638035}\nIteration: 20  Options:  {'c1': 2.030163999664059, 'c2': 0.8999999999999999, 'w': 0.455069038916464}\nIteration: 21  Options:  {'c1': 2.007240326421086, 'c2': 0.9199999999999999, 'w': 0.44972701900111317}\nIteration: 22  Options:  {'c1': 1.984374615785726, 'c2': 0.94, 'w': 0.4447370737567352}\nIteration: 23  Options:  {'c1': 1.961567461517037, 'c2': 0.96, 'w': 0.4400657894042114}\nIteration: 24  Options:  {'c1': 1.938819471237338, 'c2': 0.98, 'w': 0.4356838227118487}\nIteration: 25  Options:  {'c1': 1.916131266942353, 'c2': 1.0, 'w': 0.43156530287330336}\nIteration: 26  Options:  {'c1': 1.893503485537166, 'c2': 1.02, 'w': 0.4276873353495389}\nIteration: 27  Options:  {'c1': 1.87093677939967, 'c2': 1.04, 'w': 0.4240295880363697}\nIteration: 28  Options:  {'c1': 1.8484318169733072, 'c2': 1.06, 'w': 0.4205739443113132}\nIteration: 29  Options:  {'c1': 1.8259892833910558, 'c2': 1.08, 'w': 0.4173042107280646}\nIteration: 30  Options:  {'c1': 1.8036098811327728, 'c2': 1.1, 'w': 0.41420586961014694}\nIteration: 31  Options:  {'c1': 1.7812943307181757, 'c2': 1.12, 'w': 0.41126586872702486}\nIteration: 32  Options:  {'c1': 1.759043371437939, 'c2': 1.14, 'w': 0.4084724417486748}\nIteration: 33  Options:  {'c1': 1.7368577621255956, 'c2': 1.16, 'w': 0.40581495436664605}\nIteration: 34  Options:  {'c1': 1.7147382819731596, 'c2': 1.18, 'w': 0.4032837719146377}\nIteration: 35  Options:  {'c1': 1.6926857313936474, 'c2': 1.2, 'w': 0.4008701450750272}\nIteration: 36  Options:  {'c1': 1.6707009329339555, 'c2': 1.22, 'w': 0.39856611086172655}\nIteration: 37  Options:  {'c1': 1.6487847322418678, 'c2': 1.24, 'w': 0.39636440655637023}\nIteration: 38  Options:  {'c1': 1.626937999091311, 'c2': 1.26, 'w': 0.3942583946688905}\nIteration: 39  Options:  {'c1': 1.6051616284703591, 'c2': 1.28, 'w': 0.3922419973141236}\nIteration: 40  Options:  {'c1': 1.583456541736921, 'c2': 1.3, 'w': 0.3903096386581006}\nIteration: 41  Options:  {'c1': 1.5618236878475174, 'c2': 1.32, 'w': 0.3884561943027294}\nIteration: 42  Options:  {'c1': 1.5402640446650828, 'c2': 1.34, 'w': 0.3866769466548326}\nIteration: 43  Options:  {'c1': 1.518778620352329, 'c2': 1.36, 'w': 0.3849675454721789}\nIteration: 44  Options:  {'c1': 1.497368454857856, 'c2': 1.38, 'w': 0.3833239729009879}\nIteration: 45  Options:  {'c1': 1.4760346215029587, 'c2': 1.4, 'w': 0.38174251242096996}\nIteration: 46  Options:  {'c1': 1.454778228677894, 'c2': 1.42, 'w': 0.3802197211989471}\nIteration: 47  Options:  {'c1': 1.4336004216573355, 'c2': 1.44, 'w': 0.3787524054234573}\nIteration: 48  Options:  {'c1': 1.4125023845457787, 'c2': 1.46, 'w': 0.37733759825283403}\nIteration: 49  Options:  {'c1': 1.39148534236489, 'c2': 1.48, 'w': 0.3759725400600326}\nIteration: 50  Options:  {'c1': 1.3705505632961241, 'c2': 1.5, 'w': 0.3746546607005046}\nIteration: 51  Options:  {'c1': 1.349699361093501, 'c2': 1.52, 'w': 0.3733815635660021}\nIteration: 52  Options:  {'c1': 1.3289330976831786, 'c2': 1.54, 'w': 0.3721510112183664}\nIteration: 53  Options:  {'c1': 1.3082531859684736, 'c2': 1.56, 'w': 0.3709609124240106}\nIteration: 54  Options:  {'c1': 1.2876610928612768, 'c2': 1.58, 'w': 0.3698093104326385}\nIteration: 55  Options:  {'c1': 1.2671583425634432, 'c2': 1.6, 'w': 0.36869437236336694}\nIteration: 56  Options:  {'c1': 1.2467465201247816, 'c2': 1.62, 'w': 0.3676143795783189}\nIteration: 57  Options:  {'c1': 1.226427275307758, 'c2': 1.6400000000000001, 'w': 0.36656771893834633}\nIteration: 58  Options:  {'c1': 1.2062023267930964, 'c2': 1.6600000000000001, 'w': 0.36555287484817117}\nIteration: 59  Options:  {'c1': 1.1860734667651598, 'c2': 1.6800000000000002, 'w': 0.3645684220091819}\nIteration: 60  Options:  {'c1': 1.1660425659214986, 'c2': 1.7, 'w': 0.3636130188076487}\nIteration: 61  Options:  {'c1': 1.146111578957366, 'c2': 1.72, 'w': 0.36268540127440874}\nIteration: 62  Options:  {'c1': 1.126282550583548, 'c2': 1.74, 'w': 0.36178437755931375}\nIteration: 63  Options:  {'c1': 1.1065576221447462, 'c2': 1.76, 'w': 0.3609088228700637}\nIteration: 64  Options:  {'c1': 1.0869390389162645, 'c2': 1.78, 'w': 0.360057674830598}\nIteration: 65  Options:  {'c1': 1.0674291581692645, 'c2': 1.8, 'w': 0.35922992921908464}\nIteration: 66  Options:  {'c1': 1.0480304581097744, 'c2': 1.8199999999999998, 'w': 0.35842463604983676}\nIteration: 67  Options:  {'c1': 1.0287455478145502, 'c2': 1.8399999999999999, 'w': 0.3576408959672543}\nIteration: 68  Options:  {'c1': 1.0095771783084766, 'c2': 1.8599999999999999, 'w': 0.3568778569232281}\nIteration: 69  Options:  {'c1': 0.9905282549543739, 'c2': 1.88, 'w': 0.3561347111123839}\nIteration: 70  Options:  {'c1': 0.9716018513579736, 'c2': 1.9, 'w': 0.35541069214215554}\nIteration: 71  Options:  {'c1': 0.9528012250299461, 'c2': 1.92, 'w': 0.35470507241699134}\nIteration: 72  Options:  {'c1': 0.9341298350951458, 'c2': 1.94, 'w': 0.35401716071805106}\nIteration: 73  Options:  {'c1': 0.9155913623992082, 'c2': 1.96, 'w': 0.3533462999615839}\nIteration: 74  Options:  {'c1': 0.8971897324376921, 'c2': 1.98, 'w': 0.35269186512080497}\nIteration: 75  Options:  {'c1': 0.8789291416275995, 'c2': 2.0, 'w': 0.35205326129754305}\nIteration: 76  Options:  {'c1': 0.8608140875614461, 'c2': 2.02, 'w': 0.35142992193123407}\nIteration: 77  Options:  {'c1': 0.8428494040384126, 'c2': 2.04, 'w': 0.350821307133995}\nIteration: 78  Options:  {'c1': 0.8250403018670246, 'c2': 2.06, 'w': 0.3502269021415575}\nIteration: 79  Options:  {'c1': 0.8073924166953819, 'c2': 2.08, 'w': 0.3496462158707742}\nIteration: 80  Options:  {'c1': 0.7899118654710782, 'c2': 2.1, 'w': 0.3490787795752519}\nIteration: 81  Options:  {'c1': 0.7726053135965205, 'c2': 2.12, 'w': 0.3485241455914203}\nIteration: 82  Options:  {'c1': 0.7554800554745198, 'c2': 2.14, 'w': 0.3479818861680258}\nIteration: 83  Options:  {'c1': 0.7385441120054486, 'c2': 2.16, 'w': 0.34745159237265394}\nIteration: 84  Options:  {'c1': 0.7218063498096469, 'c2': 2.18, 'w': 0.3469328730694381}\nIteration: 85  Options:  {'c1': 0.7052766286755895, 'c2': 2.2, 'w': 0.3464253539626105}\nIteration: 86  Options:  {'c1': 0.6889659862428663, 'c2': 2.2199999999999998, 'w': 0.34592867670100746}\nIteration: 87  Options:  {'c1': 0.6728868726545348, 'c2': 2.24, 'w': 0.34544249803904953}\nIteration: 88  Options:  {'c1': 0.657053453585897, 'c2': 2.26, 'w': 0.34496648905008775}\nIteration: 89  Options:  {'c1': 0.6414820089421402, 'c2': 2.28, 'w': 0.3445003343883481}\nIteration: 90  Options:  {'c1': 0.6261914688960386, 'c2': 2.3, 'w': 0.3440437315960089}\nIteration: 91  Options:  {'c1': 0.6112041531021342, 'c2': 2.32, 'w': 0.34359639045222895}\nIteration: 92  Options:  {'c1': 0.5965468213847226, 'c2': 2.34, 'w': 0.34315803236119463}\nIteration: 93  Options:  {'c1': 0.5822522228837674, 'c2': 2.36, 'w': 0.34272838977648734}\nIteration: 94  Options:  {'c1': 0.5683614862434021, 'c2': 2.38, 'w': 0.3423072056592845}\nIteration: 95  Options:  {'c1': 0.5549280271653059, 'c2': 2.4, 'w': 0.3418942329680971}\nIteration: 96  Options:  {'c1': 0.5420244448704603, 'c2': 2.42, 'w': 0.3414892341779263}\nIteration: 97  Options:  {'c1': 0.529756065178477, 'c2': 2.44, 'w': 0.34109198082688047}\nIteration: 98  Options:  {'c1': 0.518292202077093, 'c2': 2.46, 'w': 0.34070225308844143}\nIteration: 99  Options:  {'c1': 0.5079621434110699, 'c2': 2.48, 'w': 0.3403198393677058}\nBest cost =  3.4261311762230905e-13\nBest position =  [0.99999942 0.99999884]\n"]}], "source": ["function = fx.rosen<PERSON><PERSON>    # optimum at [1,1]\n", "maxiters = 100\n", "start_opts = {'c1':2.5, 'c2':0.5, 'w':0.9}\n", "end_opts= {'c1':0.5, 'c2':2.5, 'w':0.4}     # Ref:[1]\n", "oh_strategy={ \"w\":'exp_decay', \"c1\":'nonlin_mod',\"c2\":'lin_variation'}\n", "\n", "cost, pos=optimize(function, maxiters, oh_strategy, start_opts, end_opts)\n", "\n", "print(\"Best cost = \", cost)\n", "print(\"Best position = \", pos)"]}, {"source": ["Changing options clearly shows better convergence. Just 100 iterations on the rosenbrock function are enough to bring the cost to very low orders of e-10 which is comparable to the ones in academia. [2]\n", "\n", "References:\n", "\n", "[1] <PERSON><PERSON>, <PERSON><PERSON>, et al. \"Particle swarm optimization with time-varying acceleration coefficients for the multidimensional knapsack problem.\" Applied Mathematical Modelling 38.4 (2014): 1338-1350.\n", "\n", "[2] <PERSON><PERSON><PERSON><PERSON><PERSON>, \"Comparison of particle swarm optimization variants\""], "cell_type": "markdown", "metadata": {}}], "metadata": {"kernelspec": {"display_name": "Python 3.8.5 64-bit ('pyswarms')", "language": "python", "name": "python38564bitpyswarms5956a19750cd4381a62235f4362e32ac"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "3.8.5-final"}}, "nbformat": 4, "nbformat_minor": 0}