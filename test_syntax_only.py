#!/usr/bin/env python3
"""Simple syntax test for the refactored PySwarms optimizers.

This script tests that the refactored code has correct syntax and can be imported.
"""

import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'optimagic', 'src'))

def test_imports():
    """Test that all imports work correctly."""
    print("Testing imports...")
    
    try:
        # Test basic imports
        from optimagic.optimizers.pyswarms_optimizers import (
            BasePSOOptions,
            LocalBestPSOOptions, 
            GeneralPSOOptions,
        )
        print("✓ Dataclass imports successful")
        
        from optimagic.optimizers.pyswarms_optimizers import (
            _build_pso_options_dict,
            _build_velocity_clamp,
            _create_topology_instance,
            _convert_bounds_to_pyswarms,
            _create_objective_wrapper,
            _process_pyswarms_result,
        )
        print("✓ Helper function imports successful")
        
        from optimagic.optimizers.pyswarms_optimizers import (
            PySwarmsGlobalBestPSO,
            PySwarmsLocalBestPSO,
            PySwarmsGeneralPSO,
        )
        print("✓ Optimizer class imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except SyntaxError as e:
        print(f"✗ Syntax error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_dataclass_creation():
    """Test that dataclasses can be created."""
    print("\nTesting dataclass creation...")
    
    try:
        from optimagic.optimizers.pyswarms_optimizers import (
            BasePSOOptions,
            LocalBestPSOOptions,
            GeneralPSOOptions,
        )
        
        # Test BasePSOOptions
        base_options = BasePSOOptions(
            cognitive_parameter=0.5,
            social_parameter=0.3,
            inertia_weight=0.9
        )
        print(f"✓ BasePSOOptions created successfully")
        
        # Test LocalBestPSOOptions
        local_options = LocalBestPSOOptions(
            cognitive_parameter=0.5,
            social_parameter=0.3,
            inertia_weight=0.9,
            k_neighbors=3,
            p_norm=2
        )
        print(f"✓ LocalBestPSOOptions created successfully")
        
        # Test GeneralPSOOptions
        general_options = GeneralPSOOptions(
            cognitive_parameter=0.5,
            social_parameter=0.3,
            inertia_weight=0.9,
            k_neighbors=3,
            p_norm=2,
            vonneumann_range=1
        )
        print(f"✓ GeneralPSOOptions created successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Dataclass creation failed: {e}")
        return False


def test_helper_functions():
    """Test that helper functions work without external dependencies."""
    print("\nTesting helper functions...")
    
    try:
        from optimagic.optimizers.pyswarms_optimizers import (
            BasePSOOptions,
            LocalBestPSOOptions,
            _build_pso_options_dict,
            _build_velocity_clamp,
        )
        
        # Test _build_pso_options_dict with BasePSOOptions
        base_options = BasePSOOptions(
            cognitive_parameter=0.5,
            social_parameter=0.3,
            inertia_weight=0.9
        )
        options_dict = _build_pso_options_dict(base_options)
        expected = {'c1': 0.5, 'c2': 0.3, 'w': 0.9}
        assert options_dict == expected, f"Expected {expected}, got {options_dict}"
        print("✓ _build_pso_options_dict works for BasePSOOptions")
        
        # Test _build_pso_options_dict with LocalBestPSOOptions
        local_options = LocalBestPSOOptions(
            cognitive_parameter=0.5,
            social_parameter=0.3,
            inertia_weight=0.9,
            k_neighbors=3,
            p_norm=2
        )
        options_dict = _build_pso_options_dict(local_options)
        expected = {'c1': 0.5, 'c2': 0.3, 'w': 0.9, 'k': 3, 'p': 2}
        assert options_dict == expected, f"Expected {expected}, got {options_dict}"
        print("✓ _build_pso_options_dict works for LocalBestPSOOptions")
        
        # Test _build_velocity_clamp
        velocity_clamp = _build_velocity_clamp(-1.0, 1.0)
        assert velocity_clamp == (-1.0, 1.0), f"Expected (-1.0, 1.0), got {velocity_clamp}"
        print("✓ _build_velocity_clamp works with valid inputs")
        
        velocity_clamp_none = _build_velocity_clamp(None, 1.0)
        assert velocity_clamp_none is None, f"Expected None, got {velocity_clamp_none}"
        print("✓ _build_velocity_clamp returns None with incomplete inputs")
        
        return True
        
    except Exception as e:
        print(f"✗ Helper function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all syntax tests."""
    print("Testing Refactored PySwarms Optimizers - Syntax Only")
    print("=" * 60)
    
    success = True
    success &= test_imports()
    success &= test_dataclass_creation()
    success &= test_helper_functions()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ All syntax tests passed! The refactored code is syntactically correct.")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
